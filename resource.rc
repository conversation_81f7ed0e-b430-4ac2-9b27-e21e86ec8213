#include <windows.h>

// 定义图标资源ID
#define IDI_MAIN_ICON 101
#define IDI_ICON_16 102
#define IDI_ICON_32 103
#define IDI_ICON_48 104
#define IDI_ICON_64 105
#define IDI_ICON_96 106
#define IDI_ICON_128 107
#define IDI_ICON_256 108

// 主程序图标 - 使用256x256作为主图标（支持高DPI显示）
IDI_MAIN_ICON ICON "resource/icons8_128.ico"

// 其他尺寸图标资源
IDI_ICON_16 ICON "resource/icons8_16.ico"
IDI_ICON_32 ICON "resource/icons8_32.ico"
IDI_ICON_48 ICON "resource/icons8_48.ico"
IDI_ICON_64 ICON "resource/icons8_64.ico"
IDI_ICON_96 ICON "resource/icons8_96.ico"
IDI_ICON_128 ICON "resource/icons8_128.ico"
IDI_ICON_256 ICON "resource/icons8_256.ico"

// 版本信息资源
VS_VERSION_INFO VERSIONINFO
FILEVERSION 2,0,0,1
PRODUCTVERSION 2,0,0,1
FILEFLAGSMASK VS_FFI_FILEFLAGSMASK
FILEFLAGS 0x0L
FILEOS VOS_NT_WINDOWS32
FILETYPE VFT_APP
FILESUBTYPE VFT2_UNKNOWN
BEGIN
    BLOCK "StringFileInfo"
    BEGIN
        BLOCK "040904B0"
        BEGIN
            VALUE "CompanyName", "<EMAIL>"
            VALUE "FileDescription", "EveryView"
            VALUE "FileVersion", "*******"
            VALUE "InternalName", "EveryView"
            VALUE "LegalCopyright", "Copyright (C) 2025 <EMAIL>"
            VALUE "OriginalFilename", "EveryView.exe"
            VALUE "ProductName", "EveryView"
            VALUE "ProductVersion", "*******"
            VALUE "Comments", "High performance file browser and previewer based on USN Journal"
        END
    END
    BLOCK "VarFileInfo"
    BEGIN
        VALUE "Translation", 0x409, 1200
    END
END
