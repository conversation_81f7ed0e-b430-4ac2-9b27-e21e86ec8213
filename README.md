# EveryView

## 🚀 主要特性

### 核心功能

- **超快速文件索引**快速读取整个磁盘的文件信息
- **海量文件支持**：支持处理 140 万+文件，内存占用优化至 500MB 以下
- **实时筛选搜索**：支持中文、英文、混合搜索，多关键词筛选
- **文件预览**：支持多种文件格式,文件预览功能
- **双击打开**：双击文件直接用默认程序打开，双击文件夹在资源管理器中打开

### 性能优化

- **自适应字符串长度**：支持完整长度的文件名和路径，无截断限制
- **内存优化**：动态字符串分配，减少内存浪费
- **虚拟列表视图**：支持大数据量的流畅显示
- **智能缓存**：筛选结果缓存，提升重复操作性能

### 用户界面

- **现代化界面**：扁平化设计
- **多列显示**：文件名、路径、大小、修改时间、类型
- **右键菜单**：完整的文件操作菜单
- **状态栏**：实时显示文件统计信息

## 🎯 使用方法

### 基本操作

1. **启动程序**：运行编译后的可执行文件
2. **加载文件**：加载所有磁盘文件信息
3. **浏览文件**：在左侧列表中浏览文件，右侧显示预览信息
4. **搜索筛选**：在搜索框中输入关键词进行筛选

### 高级功能

- **多关键词搜索**：使用空格分隔多个关键词
- **文件类型筛选**：支持按文件扩展名筛选
- **排序功能**：点击列标题进行排序
- **右键操作**：右键文件可进行打开、复制路径等操作

## 🖱️ 右键菜单功能详解

### 右键菜单选项

#### 📂 打开文件夹 (Open Folder)

- **功能**：在 Windows 资源管理器中打开文件所在的文件夹
- **适用对象**：文件和文件夹
- **快捷键**：无
- **说明**：
  - 对于文件：打开文件所在的父文件夹，并选中该文件
  - 对于文件夹：直接在资源管理器中打开该文件夹

#### 📄 打开文件 (Open File)

- **功能**：使用系统默认程序打开选中的文件
- **适用对象**：仅文件（文件夹不显示此选项）
- **快捷键**：双击文件也可实现相同功能
- **说明**：
- 自动调用 Windows Shell 关联的默认程序
- 支持所有 Windows 支持的文件类型
- 如果文件类型未关联程序，会弹出"打开方式"对话框

#### 📋 复制路径 (Copy Path)

- **功能**：将文件或文件夹的完整路径复制到剪贴板
- **适用对象**：文件和文件夹
- **快捷键**：无
- **说明**：
- 复制的是完整的绝对路径
- 可直接粘贴到其他程序中使用
- 路径格式：`C:\完整\路径\文件名.扩展名`

#### ⚙️ 属性 (Properties)

- **功能**：显示文件或文件夹的 Windows 属性对话框
- **适用对象**：文件和文件夹
- **快捷键**：无
- **说明**：
- 显示文件大小、创建时间、修改时间等详细信息
- 可查看和修改文件属性（只读、隐藏等）
- 对于可执行文件，显示版本信息

---

### 程序设置功能

#### 🌐 语言 (Language)

- **功能**：切换程序界面语言
- **适用对象**：全局设置，不依赖选中文件
- **子菜单选项**：
  - **中文 (Chinese)**：切换到中文界面
  - **English**：切换到英文界面
- **说明**：
  - 语言切换后立即生效
  - 影响所有界面文本、菜单、按钮等
  - 设置会自动保存，下次启动时保持选择的语言

#### 🔤 字体 (Font)

- **功能**：调整程序界面字体设置
- **适用对象**：全局设置，影响整个程序界面
- **子菜单选项**：
- **字体选择**：选择不同的字体类型
- **字体样式**：选择字体样式（常规、粗体、斜体等）
- **说明**：
- 字体更改会影响文件列表、预览区域、菜单等所有文本显示
- 支持系统安装的所有字体
- 字体设置会自动保存

#### ➕ 增大字体 (Increase Font)

- **功能**：快速增大程序界面字体大小
- **适用对象**：全局设置
- **快捷键**：Ctrl + Plus (+)
- **说明**：
  - 每次点击增大字体 2 个点数
  - 最大字体大小限制为 72pt
  - 适合视力不佳或高分辨率显示器用户
  - 立即生效，无需重启程序

#### ➖ 减小字体 (Decrease Font)

- **功能**：快速减小程序界面字体大小
- **适用对象**：全局设置
- **快捷键**：Ctrl + Minus (-)
- **说明**：
  - 每次点击减小字体 2 个点数
  - 最小字体大小限制为 8pt
  - 适合在小屏幕上显示更多内容
  - 立即生效，无需重启程序

### 右键菜单使用方法

#### 基本操作步骤

1. **选择目标**：在文件列表中找到要操作的文件或文件夹
2. **右键点击**：在目标项目上点击鼠标右键
3. **选择功能**：从弹出的上下文菜单中选择所需功能
4. **执行操作**：点击菜单项执行相应操作

#### 智能菜单显示

**文件操作区域**（上半部分）：

- **文件**：显示"打开文件夹"、"打开文件"、"复制路径"、"属性"四个选项
- **文件夹**：显示"打开文件夹"、"复制路径"、"属性"三个选项（隐藏"打开文件"）
- **无效项目**：如果选中的项目无效，不显示文件操作选项

**程序设置区域**（下半部分）：

- **语言设置**：始终显示，包含中文/English 子菜单
- **字体设置**：始终显示，包含字体选择子菜单
- **字体大小调整**：始终显示"增大字体"和"减小字体"选项

#### 菜单分组说明

右键菜单分为两个功能区域：

1. **文件操作区域**：

   - 针对选中的文件或文件夹进行操作
   - 根据选中项目类型动态显示相关选项
   - 包含分隔线与程序设置区域分开

2. **程序设置区域**：
   - 全局程序设置，不依赖选中的文件
   - 始终显示，方便用户随时调整程序设置
   - 包含语言、字体相关的所有设置选项


### 使用场景示例

#### 场景 1：快速定位文件

1. 在搜索结果中找到目标文件
2. 右键选择"打开文件夹"
3. 系统自动打开资源管理器并定位到该文件

#### 场景 2：批量路径收集

1. 逐个右键需要的文件
2. 选择"复制路径"
3. 粘贴到文本编辑器中收集路径列表

#### 场景 3：文件属性查看

1. 右键可疑或重要文件
2. 选择"属性"
3. 查看详细的文件信息和安全属性

#### 场景 4：界面语言切换

1. 在任意位置右键（无需选中文件）
2. 选择"Language" → "English" 或 "中文"
3. 界面立即切换到选择的语言

#### 场景 5：字体大小快速调整

1. 在任意位置右键
2. 选择"Increase Font"或"Decrease Font"
3. 字体大小立即调整，适应不同显示需求

#### 场景 6：自定义字体设置

1. 右键选择"Font"
2. 从子菜单中选择喜欢的字体类型
3. 整个程序界面字体立即更新

#### 场景 7：高分辨率显示器优化

1. 在 4K 或高 DPI 显示器上，文字可能显示过小
2. 右键选择"Increase Font"多次点击
3. 调整到舒适的字体大小
4. 设置自动保存，下次启动保持设置


#### 文件操作相关

- **权限要求**：某些系统文件可能需要管理员权限才能访问
- **路径长度**：支持 Windows 最大路径长度（260 字符标准路径，32767 字符长路径）
- **特殊字符**：正确处理文件名中的特殊字符和 Unicode 字符
- **网络路径**：支持网络共享路径的操作

#### 程序设置相关

- **语言切换**：

  - 语言设置立即生效，无需重启程序
  - 设置会保存到配置文件，下次启动时自动加载
  - 如果配置文件损坏，程序会使用系统默认语言

- **字体设置**：

  - 字体更改会影响整个程序界面
  - 建议选择系统预装的标准字体以确保兼容性
  - 某些特殊字体可能不支持所有字符，导致显示异常
  - 字体大小调整范围：8pt - 72pt

- **快捷键冲突**：

  - Ctrl + Plus/Minus 可能与其他程序快捷键冲突
  - 如遇冲突，建议使用右键菜单方式调整字体

- **显示器兼容性**：
  - 在高 DPI 显示器上，建议适当增大字体以提高可读性
  - 在低分辨率显示器上，可适当减小字体以显示更多内容
  - 程序支持 Windows DPI 缩放，但手动字体调整优先级更高

## 🚀 性能指标

- **文件加载速度**：140 万文件 < 10 秒
- **内存占用**：约 500MB (140 万文件)
- **搜索响应时间**：< 100ms
- **UI 刷新率**：60fps (虚拟列表)


## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目！

### 代码规范

- 使用 UTF-8 编码
- 遵循 Windows API 命名约定
- 添加适当的注释和错误处理

## 📄 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：<EMAIL>





- 发送邮件至项目维护者

**注意**：本项目仅供学习和研究使用，请遵守相关法律法规。
