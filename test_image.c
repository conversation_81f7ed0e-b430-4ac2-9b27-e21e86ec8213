#ifndef UNICODE
#define UNICODE
#endif
#ifndef _UNICODE
#define _UNICODE
#endif
#include <windows.h>
#include <commctrl.h>
#include <stdio.h>

// 测试图片显示功能
HWND hwndMain = NULL;
HWND hwndImage = NULL;

// 创建图片控件
void CreateImageControl(HWND parent)
{
    if (hwndImage && IsWindow(hwndImage))
        return;

    // 创建图片控件
    hwndImage = CreateWindowW(L"STATIC", NULL,
                              WS_CHILD | WS_VISIBLE | SS_BITMAP | SS_CENTERIMAGE,
                              10, 10, 150, 150,
                              parent, NULL, GetModuleHandle(NULL), NULL);
}

// 显示图片
void DisplayImage(const wchar_t *imagePath)
{
    if (!hwndImage || !IsWindow(hwndImage))
        return;
        
    // 检查文件是否存在
    if (GetFileAttributesW(imagePath) == INVALID_FILE_ATTRIBUTES)
    {
        MessageBoxW(hwndMain, L"图片文件不存在", L"错误", MB_OK);
        return;
    }
        
    // 加载图片
    HBITMAP hBitmap = (HBITMAP)LoadImageW(NULL, imagePath, IMAGE_BITMAP, 0, 0, 
                                          LR_LOADFROMFILE | LR_DEFAULTSIZE);
    if (hBitmap)
    {
        // 设置图片到控件
        SendMessage(hwndImage, STM_SETIMAGE, IMAGE_BITMAP, (LPARAM)hBitmap);
        MessageBoxW(hwndMain, L"图片加载成功", L"成功", MB_OK);
    }
    else
    {
        MessageBoxW(hwndMain, L"图片加载失败", L"错误", MB_OK);
    }
}

// 窗口过程
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    switch (uMsg)
    {
    case WM_CREATE:
        CreateImageControl(hwnd);
        // 延迟加载图片
        SetTimer(hwnd, 1, 1000, NULL);
        return 0;
        
    case WM_TIMER:
        if (wParam == 1)
        {
            KillTimer(hwnd, 1);
            DisplayImage(L"resource\\zanshangma.bmp");
        }
        return 0;

    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow)
{
    // 注册窗口类
    WNDCLASSW wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = L"TestImageWindow";
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    
    RegisterClassW(&wc);
    
    // 创建窗口
    hwndMain = CreateWindowW(L"TestImageWindow", L"图片显示测试",
                             WS_OVERLAPPEDWINDOW,
                             100, 100, 400, 300,
                             NULL, NULL, hInstance, NULL);
    
    ShowWindow(hwndMain, nCmdShow);
    UpdateWindow(hwndMain);
    
    // 消息循环
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
