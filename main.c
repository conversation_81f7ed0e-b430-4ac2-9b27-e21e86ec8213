#ifndef UNICODE
#define UNICODE
#endif
#ifndef _UNICODE
#define _UNICODE
#endif
#include <windows.h>
#include <commctrl.h>
#include <stdio.h>
#include <locale.h>
#include <string.h>
#include <wchar.h>
#include <time.h>
#include <shlwapi.h>
#include <shellapi.h>
#include <commdlg.h>
#include <shlobj.h>
#include <gdiplus.h>

#include "usn_journal.h"

#define MAX_DISPLAY_ITEMS 20000
#define MAX_ICON_CACHE_SIZE 1000 // 图标缓存的最大条目数

// 图标缓存结构
typedef struct
{
    WCHAR extension[32]; // 文件扩展名
    int iconIndex;       // 对应的图标索引
    BOOL isDirectory;    // 是否是目录
} IconCacheEntry;

// DPI awareness constants and types (for compatibility)
// Note: DPI_AWARENESS_CONTEXT is already defined in newer Windows headers
// Only define if not already available

#ifndef DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2
#define DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2 ((DPI_AWARENESS_CONTEXT) - 4)
#endif

#ifndef PROCESS_PER_MONITOR_DPI_AWARE
typedef enum PROCESS_DPI_AWARENESS
{
    PROCESS_DPI_UNAWARE = 0,
    PROCESS_SYSTEM_DPI_AWARE = 1,
    PROCESS_PER_MONITOR_DPI_AWARE = 2
} PROCESS_DPI_AWARENESS;
#endif

#ifndef WM_DPICHANGED
#define WM_DPICHANGED 0x02E0
#endif

// Function declarations for DPI awareness (for compatibility)
#ifndef DECLSPEC_IMPORT
#define DECLSPEC_IMPORT __declspec(dllimport)
#endif

DECLSPEC_IMPORT BOOL WINAPI SetProcessDPIAware(void);

// 资源定义
#define IDI_MAIN_ICON 101

// 右键菜单ID定义
#define ID_OPEN_FOLDER 1001
#define ID_OPEN_FILE 1002
#define ID_COPY_PATH 1003
#define ID_PROPERTIES 1004
#define ID_LANGUAGE_CHINESE 1005
#define ID_LANGUAGE_ENGLISH 1006
#define ID_FONT_NORMAL 1007
#define ID_FONT_BOLD 1008
#define ID_FONT_INCREASE 1009
#define ID_FONT_DECREASE 1010
#define ID_IMAGE_CONTROL 1011

#pragma comment(lib, "comctl32.lib")
#pragma comment(lib, "gdi32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "shlwapi.lib")
#pragma comment(lib, "advapi32.lib")

// Constants for flat design
#define DEFAULT_WINDOW_WIDTH 1200
#define DEFAULT_WINDOW_HEIGHT 800
#define SPLITTER_WIDTH 1 // 1px splitter
// #define FONT_SIZE 22          // 已移除：现在使用 g_currentFontSize 动态字号
#define LINE_HEIGHT 30       // 30px line height
#define BORDER_WIDTH 0       // 0px gaps between windows
#define INPUT_HEIGHT 22      // Input box height
#define STATUS_HEIGHT 30     // Status bar height
#define MAX_FILTER_LENGTH 32 // Maximum filter text length
#define CONFIG_FILE "config.ini"

// Colors for flat design
#define FLAT_COLOR_BACKGROUND RGB(240, 240, 240)
#define FLAT_COLOR_BORDER RGB(200, 200, 200)
#define FLAT_COLOR_SPLITTER RGB(180, 180, 180)
#define FLAT_COLOR_TEXT RGB(50, 50, 50)

// 语言枚举
typedef enum
{
    APP_LANG_CHINESE = 0,
    APP_LANG_ENGLISH = 1
} Language;

// 语言字符串结构
typedef struct
{
    const WCHAR *columnNames[4];   // 列名：名称、路径、大小、修改时间
    const WCHAR *menuOpenFolder;   // 打开文件夹
    const WCHAR *menuOpenFile;     // 打开文件
    const WCHAR *menuCopyPath;     // 复制路径
    const WCHAR *menuProperties;   // 属性
    const WCHAR *menuLanguage;     // 语言
    const WCHAR *menuChinese;      // 中文
    const WCHAR *menuEnglish;      // English
    const WCHAR *statusReady;      // 就绪
    const WCHAR *statusScanning;   // 正在扫描
    const WCHAR *statusFiltering;  // 正在筛选
    const WCHAR *statusSorting;    // 正在排序
    const WCHAR *statusFiles;      // 文件
    const WCHAR *statusFolders;    // 文件夹
    const WCHAR *previewPrompt;    // 请选择一个文件进行预览
    const WCHAR *folderSizeText;   // 文件夹
    const WCHAR *windowTitle;      // 窗口标题
    const WCHAR *fileNotFound;     // 文件不存在或无法访问
    const WCHAR *fileInfoError;    // 无法获取文件信息
    const WCHAR *filterIndexError; // 筛选索引错误
    const WCHAR *binaryFileInfo;   // 二进制文件信息
    const WCHAR *previewTemplate;  // 预览模板
    const WCHAR *fileContent;      // 文件内容
    const WCHAR *fileTooLarge;     // 文件过大
    const WCHAR *cannotReadFile;   // 无法读取文件
    const WCHAR *imageFileInfo;    // 图片文件信息
    const WCHAR *loadComplete;     // 加载完成
    const WCHAR *scanProgress;     // 扫描进度
    const WCHAR *scanComplete;     // 扫描完成
    const WCHAR *usnReadFailed;    // USN读取失败
} LanguageStrings;

// Configuration structure (窗口和UI设置)
typedef struct
{
    int windowX, windowY;
    int windowWidth, windowHeight;
    BOOL windowMaximized;
    int splitterPosition;
    Language currentLanguage; // 当前语言

    // ListView column settings
    int columnWidths[4]; // 列宽度：名称、路径、大小、修改时间
    int columnOrder[4];  // 列顺序：0=名称, 1=路径, 2=大小, 3=修改时间

    // Font settings
    int fontSize;     // 字体大小 (10-24px)
    BOOL useBoldFont; // 是否使用加粗字体
} ConfigData;

// Global variables
HWND hwndMain = NULL;
HWND hwndStatus = NULL;
HWND hwndLeft = NULL;
HWND hwndRight = NULL;
HWND hwndEdit = NULL;
HWND hwndImage = NULL; // 图片显示控件
HFONT hFont = NULL;
HFONT hFontBold = NULL;        // 加粗字体
HBITMAP hBitmap = NULL;        // 图片位图
BOOL g_useBoldFont = FALSE;    // 字体状态：FALSE=正常(微软雅黑Light)，TRUE=加粗(微软雅黑)
int g_currentFontSize = 16;    // 当前字号，默认16px
int g_currentInputHeight = 22; // 当前输入框高度，根据字体大小动态计算

// 图片控件状态跟踪，避免重复操作
static BOOL g_imageControlCreated = FALSE;
static DWORD g_lastImageUpdateTime = 0;

// 检查图片控件是否有效
BOOL IsImageControlValid()
{
    BOOL isValid = hwndImage && IsWindow(hwndImage);
    if (!isValid)
    {
        g_imageControlCreated = FALSE; // 重置状态
    }
    return isValid;
}

// 枚举子窗口的回调函数，用于清理所有图片控件
BOOL CALLBACK EnumChildProc(HWND hwnd, LPARAM lParam)
{
    WCHAR className[256];
    GetClassNameW(hwnd, className, 256);

    // 如果是STATIC控件且有SS_BITMAP样式，可能是图片控件
    if (wcscmp(className, L"STATIC") == 0)
    {
        LONG style = GetWindowLong(hwnd, GWL_STYLE);
        if (style & SS_BITMAP)
        {
            // 检查控件ID是否匹配
            int id = GetDlgCtrlID(hwnd);
            if (id == ID_IMAGE_CONTROL)
            {
                DestroyWindow(hwnd);
            }
        }
    }
    return TRUE; // 继续枚举
}

// 清理图片控件 - 强力版本，清理所有可能的图片控件
void CleanupImageControl()
{
    // 首先清理已知的图片控件句柄
    if (hwndImage)
    {
        if (IsWindow(hwndImage))
        {
            DestroyWindow(hwndImage);
        }
        hwndImage = NULL;
    }

    // 枚举右侧面板的所有子窗口，清理可能的图片控件
    if (hwndRight && IsWindow(hwndRight))
    {
        EnumChildWindows(hwndRight, EnumChildProc, 0);
    }

    g_imageControlCreated = FALSE;
}

// 在预览区域显示图片 - 修复重复创建问题
void CreateSingleImageControl()
{
    if (!hwndRight || !IsWindow(hwndRight))
        return;

    // 如果图片控件已经创建并且有效，直接返回
    if (IsImageControlValid() && g_imageControlCreated)
        return;

    // 强制清理所有可能存在的图片控件
    CleanupImageControl();

    RECT rect;
    GetClientRect(hwndRight, &rect);

    // 计算图片控件的位置和大小 - 固定在可见区域的右下角
    int imageWidth = 150;
    int imageHeight = 150;
    int x = rect.right - imageWidth - 20;   // 距离右边20像素
    int y = rect.bottom - imageHeight - 20; // 距离底部20像素

    // 确保位置不会超出边界
    if (x < 0)
        x = 0;
    if (y < 0)
        y = 0;

    // 创建图片控件 - 使用WS_CLIPSIBLINGS防止被其他控件遮挡
    hwndImage = CreateWindowW(L"STATIC", NULL,
                              WS_CHILD | WS_VISIBLE | SS_BITMAP | SS_CENTERIMAGE | WS_CLIPSIBLINGS,
                              x, y, imageWidth, imageHeight,
                              hwndRight, (HMENU)ID_IMAGE_CONTROL, GetModuleHandle(NULL), NULL);

    if (hwndImage)
    {
        // 加载默认BMP图片
        HBITMAP hBitmap = (HBITMAP)LoadImageW(NULL, L"resource\\zanshangma.bmp", IMAGE_BITMAP,
                                              imageWidth, imageHeight, LR_LOADFROMFILE | LR_CREATEDIBSECTION);

        if (hBitmap)
        {
            SendMessage(hwndImage, STM_SETIMAGE, IMAGE_BITMAP, (LPARAM)hBitmap);
            // 确保控件在最顶层并可见
            SetWindowPos(hwndImage, HWND_TOP, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
            ShowWindow(hwndImage, SW_SHOW); // 确保控件可见
            g_imageControlCreated = TRUE;   // 标记创建成功
        }
        else
        {
            // 如果加载失败，尝试加载PNG格式
            hBitmap = (HBITMAP)LoadImageW(NULL, L"resource\\zanshangma.png", IMAGE_BITMAP,
                                          imageWidth, imageHeight, LR_LOADFROMFILE | LR_CREATEDIBSECTION);
            if (hBitmap)
            {
                SendMessage(hwndImage, STM_SETIMAGE, IMAGE_BITMAP, (LPARAM)hBitmap);
                SetWindowPos(hwndImage, HWND_TOP, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE);
                ShowWindow(hwndImage, SW_SHOW);
                g_imageControlCreated = TRUE;
            }
            else
            {
                // 如果都加载失败，销毁控件
                DestroyWindow(hwndImage);
                hwndImage = NULL;
                g_imageControlCreated = FALSE;
            }
        }
    }
}

// 更新图片控件位置 - 确保始终在可见区域的右下角
void UpdateImagePosition()
{
    if (!IsImageControlValid() || !hwndRight)
        return;

    // 防抖：限制更新频率
    DWORD currentTime = GetTickCount();
    if (currentTime - g_lastImageUpdateTime < 50) // 50ms防抖
        return;

    RECT rightRect;
    GetClientRect(hwndRight, &rightRect);

    // 重新定位图片控件到可见区域的右下角
    int imageWidth = 150;
    int imageHeight = 150;
    int x = rightRect.right - imageWidth - 20;
    int y = rightRect.bottom - imageHeight - 20;

    // 确保位置不会超出边界
    if (x < 0)
        x = 0;
    if (y < 0)
        y = 0;

    // 设置位置并确保控件可见且在最顶层
    SetWindowPos(hwndImage, HWND_TOP,
                 x, y, imageWidth, imageHeight,
                 SWP_NOACTIVATE | SWP_SHOWWINDOW);
    g_lastImageUpdateTime = currentTime; // 更新时间戳
}

// 在已创建的图片控件中显示图片 - 优化版本，避免重复更新位置
void DisplayImageInPreview(const wchar_t *imagePath)
{
    if (!IsImageControlValid())
        return;

    // 检查文件是否存在
    if (GetFileAttributesW(imagePath) == INVALID_FILE_ATTRIBUTES)
        return;

    // 加载图片
    HBITMAP hBitmap = (HBITMAP)LoadImageW(NULL, imagePath, IMAGE_BITMAP, 0, 0,
                                          LR_LOADFROMFILE | LR_DEFAULTSIZE);
    if (hBitmap)
    {
        // 设置图片到控件
        SendMessage(hwndImage, STM_SETIMAGE, IMAGE_BITMAP, (LPARAM)hBitmap);

        // 显示控件并确保在最顶层
        ShowWindow(hwndImage, SW_SHOW);
        SetWindowPos(hwndImage, HWND_TOP, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE | SWP_NOACTIVATE);

        // 立即更新位置以确保图片正确显示
        UpdateImagePosition();
    }
}

// Virtual ListView architecture
static BOOL g_dataLoaded = FALSE;
static BOOL g_mainWindowReady = FALSE;
static BOOL g_useVirtualMode = TRUE;
static CRITICAL_SECTION g_dataCriticalSection;

// Virtual ListView data
static int g_virtualItemCount = 0;
static BOOL g_dataLoadInProgress = FALSE;

// Virtual ListView filtering - 使用位图优化内存
static BYTE *g_filterBitmap = NULL;     // 筛选位图，每个文件1位
static int g_filteredCount = 0;         // 筛选后的数量
static BOOL g_filterActive = FALSE;     // 是否有活动筛选
static int g_bitmapSize = 0;            // 位图大小（字节数）
static BOOL g_filterInProgress = FALSE; // 筛选是否正在进行中

// 筛选索引缓存（优化GetFilteredIndexByPosition性能）
static int *g_filterIndexCache = NULL;
static int g_filterIndexCacheSize = 0;
static BOOL g_filterIndexCacheValid = FALSE;

// Data reload functionality
static UINT_PTR g_reloadTimerId = 0;
static BOOL g_autoReloadEnabled = TRUE;
HBRUSH hBackgroundBrush = NULL;
HBRUSH hSplitterBrush = NULL;
int splitterPos = 400;
BOOL isDragging = FALSE;

// 自适应长度的USN内存数据结构
typedef struct
{
    WCHAR *fileName;     // 指向动态分配的文件名字符串
    WCHAR *fullPath;     // 指向动态分配的完整路径字符串
    DWORD fileSize;      // 32位，支持4GB文件
    DWORD lastWriteTime; // 32位时间戳
    BYTE isDirectory;    // 1字节：1=文件夹，0=文件
    // 移除预计算的字符串，改为动态生成以节省内存
    // sizeStr和timeStr在显示时动态计算
} USNFileData;

// USN Memory variables
USNFileData *g_usnFiles = NULL;
int g_usnFileCount = 0;
int g_usnFileCapacity = 0;
CRITICAL_SECTION g_usnDataCriticalSection;

// Filtering variables
WCHAR g_currentFilter[MAX_FILTER_LENGTH] = L"";

// 筛选缓存优化
WCHAR g_lastFilterText[MAX_FILTER_LENGTH] = L"";
BOOL g_filterCacheValid = FALSE;

// DPI 自适应相关变量
static int g_dpiX = 96;          // 当前DPI X
static int g_dpiY = 96;          // 当前DPI Y
static float g_dpiScaleX = 1.0f; // DPI缩放比例 X
static float g_dpiScaleY = 1.0f; // DPI缩放比例 Y
static BOOL g_dpiAware = FALSE;  // DPI感知状态

// 排序相关变量
int g_sortColumn = -1;         // 当前排序列，-1表示未排序
BOOL g_sortAscending = TRUE;   // TRUE=升序，FALSE=降序
HANDLE g_sortThread = NULL;    // 排序线程句柄
BOOL g_sortInProgress = FALSE; // 排序进行中标志

// 筛选线程相关变量
HANDLE g_filterThread = NULL;
DWORD g_filterThreadId = 0;
CRITICAL_SECTION g_filterCriticalSection;
BOOL g_filterThreadRunning = FALSE;
BOOL g_filterPending = FALSE;
WCHAR g_pendingFilterText[256] = L"";

// Configuration variables
ConfigData g_config = {0};
HANDLE g_fileWatchThread = NULL;
HANDLE g_usnReadThread = NULL;
BOOL g_fileWatchActive = FALSE;
BOOL g_usnReadInProgress = FALSE;

// 文件图标相关变量
HIMAGELIST g_hImageList = NULL;
SHFILEINFOW g_shFileInfo = {0};

// 图标缓存相关变量
IconCacheEntry g_iconCache[MAX_ICON_CACHE_SIZE];
int g_iconCacheCount = 0;
CRITICAL_SECTION g_iconCacheCriticalSection;

// 滚动优化相关变量
BOOL g_isScrolling = FALSE;
DWORD g_lastScrollTime = 0;

// 语言相关全局变量
Language g_currentLanguage = APP_LANG_CHINESE;
LanguageStrings g_strings;

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void UpdateLayout(HWND hwnd);
void CreateFlatFont();
void CreateBoldFont();
void SetFontStyle(BOOL useBold);
void SetFontSize(int fontSize);
void RecreateAllFonts();
void UpdateAllControlsFonts();
int CalculateLineHeight(int fontSize);
void UpdateInputHeight();
void UpdateListViewRowHeight();
void DrawSplitter(HDC hdc, RECT *rect);

// DPI awareness functions
void InitializeDPIAwareness();
void UpdateDPISettings(HWND hwnd);
int ScaleDPIX(int value);
int ScaleDPIY(int value);
HFONT CreateScaledFont(int baseSize, const WCHAR *fontName);
HFONT CreateScaledBoldFont(int baseSize, const WCHAR *fontName);
void HandleDPIChanged(HWND hwnd, WPARAM wParam, LPARAM lParam);

// Filtering functions
void ApplyFilter(const WCHAR *filterText);
BOOL MatchesFilter(const WCHAR *filename, const WCHAR *path, const WCHAR *filter);
BOOL MatchesSingleKeyword(const WCHAR *filename, const WCHAR *keyword);
BOOL MatchesMultipleKeywords(const WCHAR *filename, const WCHAR *filter);
WCHAR *FindSubstringIgnoreCase(const WCHAR *haystack, const WCHAR *needle);
WCHAR *FindEnglishSubstringIgnoreCase(const WCHAR *haystack, const WCHAR *needle);
void PrepareFilterContext(const WCHAR *needle);
void SafeCleanFilterText(WCHAR *text, int maxLen);
void UpdateStatusBar();
void OnFilterTextChanged();

// Virtual ListView functions
LRESULT HandleVirtualListViewNotify(NMLVDISPINFOW *pDispInfo);
LRESULT CALLBACK ListViewSubclassProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData);
LRESULT CALLBACK PreviewSubclassProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData);
void ForceRefreshListView();

// USN file tree rendering functions
void InitializeUSNMemory();
void CleanupUSNMemory();
void AddUSNFile(const WCHAR *fileName, const WCHAR *fullPath, LONGLONG fileSize, FILETIME lastWriteTime, DWORD fileAttributes);
WCHAR *SafeAllocateString(const WCHAR *source);

// 筛选线程函数
DWORD WINAPI FilterThreadProc(LPVOID lpParam);

// 排序函数
int CompareUSNFiles(const void *a, const void *b);
void SortFileList(int column);
void ReverseFileList();
void SortFilteredResults(int column);
DWORD WINAPI SortThreadProc(LPVOID lpParam);
void InitializeFilterThread();
void CleanupFilterThread();
void RequestFilter(const WCHAR *filterText);
void PerformFilterOperation(const WCHAR *filterText);
void PerformBatchFiltering(const WCHAR *filterText);
void PerformBatchFilteringNoLock(const WCHAR *filterText, int fileCount);

// 位图筛选辅助函数
void InitializeFilterBitmap();
void CleanupFilterBitmap();
void SetFilterBit(int index, BOOL value);
BOOL GetFilterBit(int index);
int GetFilteredIndexByPosition(int position);
void InvalidateFilterCache();

// 右键菜单功能函数
HMENU CreateFileContextMenu();
void HandleListViewRightClick(LPNMITEMACTIVATE pnmia);
void HandleListViewDoubleClick(LPNMITEMACTIVATE pnmia);
int GetActualFileIndex(int selectedIndex);
void BuildFullFilePath(int actualIndex, WCHAR *fullPath);
void BuildFullFolderPath(int actualIndex, WCHAR *fullPath);
void ShowContextMenu(POINT pt, const WCHAR *filePath);
void HandleMenuCommand(int cmd, const WCHAR *filePath);
void OpenFileFolder(const WCHAR *filePath);
void OpenFileWithDefaultApp(const WCHAR *filePath);
void CopyPathToClipboard(const WCHAR *filePath);
void ShowFileProperties(const WCHAR *filePath);
int ReadUSNToMemory();
void RenderUSNFileTree();

// 文件图标相关函数
void InitializeFileIcons();
void CleanupFileIcons();
int GetFileIconIndex(const WCHAR *fileName, BOOL isDirectory);
int GetCachedFileIconIndex(const WCHAR *fileName, BOOL isDirectory);
void InitializeIconCache();
void CleanupIconCache();

// 语言相关函数
void InitializeLanguageStrings();
void SetLanguage(Language lang);
void UpdateUILanguage();
void UpdateColumnHeaders();
void UpdateWindowTitle();
void SetStatusBarText(const WCHAR *message);

// Configuration functions
void LoadConfiguration();
void SaveConfiguration();
void ApplyConfiguration();
void CreateDefaultConfigFile();
void SaveColumnSettings();
void AutoAdjustNameColumnWidth();
void AutoAdjustColumnWidth(int columnIndex);
void AutoAdjustAllColumnWidths();

// USN Journal functions
DWORD WINAPI USNReadThreadProc(LPVOID lpParam);
void StartUSNReadThread();

// File monitoring functions
DWORD WINAPI FileWatchThreadProc(LPVOID lpParam);
void StartFileWatcher();
void StopFileWatcher();

// File preview functions
void PreviewSelectedFile();
void LoadFilePreview(const WCHAR *filePath);
void LoadReadmePreview();
void CreateSingleImageControl();
void CleanupImageControl();
void UpdateImagePosition();
void DisplayImageInPreview(const wchar_t *imagePath);
BOOL IsTextFile(const WCHAR *filePath);
BOOL IsImageFile(const WCHAR *filePath);

// Utility functions
void SetControlTextUTF8(HWND hwnd, const WCHAR *text);

// Set control text with Unicode support
void SetControlTextUTF8(HWND hwnd, const WCHAR *text)
{
    if (!hwnd || !text)
        return;
    SetWindowTextW(hwnd, text);
}

// 优化的筛选匹配函数 - 减少字符串操作开销
BOOL MatchesFilter(const WCHAR *filename, const WCHAR *path, const WCHAR *filter)
{
    if (!filter || wcslen(filter) == 0)
        return TRUE;

    // 快速单关键词匹配（大多数情况）
    if (wcschr(filter, L' ') == NULL)
    {
        return MatchesSingleKeyword(filename, filter);
    }

    // 多关键词匹配
    return MatchesMultipleKeywords(filename, filter);
}

// 单关键词快速匹配（避免字符串复制）
BOOL MatchesSingleKeyword(const WCHAR *filename, const WCHAR *keyword)
{
    if (!filename || !keyword)
        return FALSE;

    // 使用优化的不区分大小写搜索
    return FindSubstringIgnoreCase(filename, keyword) != NULL;
}

// 多关键词匹配（优化中文支持）
BOOL MatchesMultipleKeywords(const WCHAR *filename, const WCHAR *filter)
{
    WCHAR filterCopy[MAX_FILTER_LENGTH];
    wcscpy(filterCopy, filter);

    WCHAR *token;
    WCHAR *context = NULL;
    token = wcstok_s(filterCopy, L" ", &context);

    while (token != NULL)
    {
        // 跳过空token
        if (wcslen(token) == 0)
        {
            token = wcstok_s(NULL, L" ", &context);
            continue;
        }

        // 分析当前关键词的字符类型
        BOOL hasChineseInToken = FALSE;
        BOOL hasEnglishInToken = FALSE;
        int tokenLen = wcslen(token);

        for (int i = 0; i < tokenLen; i++)
        {
            WCHAR ch = token[i];
            if (ch >= 0x4E00 && ch <= 0x9FFF)
                hasChineseInToken = TRUE;
            else if ((ch >= L'A' && ch <= L'Z') || (ch >= L'a' && ch <= L'z'))
                hasEnglishInToken = TRUE;
        }

        // 根据关键词类型选择最优匹配方法
        BOOL found = FALSE;
        if (hasChineseInToken && !hasEnglishInToken)
        {
            // 纯中文关键词：精确匹配
            found = (wcsstr(filename, token) != NULL);
        }
        else if (!hasChineseInToken && hasEnglishInToken)
        {
            // 纯英文关键词：忽略大小写匹配
            found = (FindEnglishSubstringIgnoreCase(filename, token) != NULL);
        }
        else
        {
            // 混合关键词：使用通用匹配
            found = (FindSubstringIgnoreCase(filename, token) != NULL);
        }

        if (!found)
        {
            return FALSE;
        }

        token = wcstok_s(NULL, L" ", &context);
    }

    return TRUE;
}

// 筛选模式枚举
typedef enum
{
    FILTER_MODE_PURE_CHINESE = 1,
    FILTER_MODE_PURE_ENGLISH = 2,
    FILTER_MODE_MIXED = 3,
    FILTER_MODE_GENERAL = 0
} FilterMode;

// 筛选上下文结构（避免重复分析）
typedef struct
{
    FilterMode mode;
    WCHAR pattern[MAX_FILTER_LENGTH];
    WCHAR patternLower[MAX_FILTER_LENGTH]; // 预计算的小写版本
    int patternLen;
    BOOL hasNumbers;
} FilterContext;

// 全局筛选上下文缓存
static FilterContext g_filterContext = {0};
static BOOL g_filterContextValid = FALSE;

// 预处理筛选模式（只在筛选文本改变时调用一次）
void PrepareFilterContext(const WCHAR *needle)
{
    // 首先清空上下文，防止使用无效数据
    memset(&g_filterContext, 0, sizeof(FilterContext));
    g_filterContextValid = FALSE;

    if (!needle)
    {
        return;
    }

    int needleLen = wcslen(needle);
    if (needleLen == 0 || needleLen >= MAX_FILTER_LENGTH)
    {
        return;
    }

    // 安全复制原始模式
    wcsncpy(g_filterContext.pattern, needle, MAX_FILTER_LENGTH - 1);
    g_filterContext.pattern[MAX_FILTER_LENGTH - 1] = L'\0';
    g_filterContext.patternLen = needleLen;

    // 分析字符类型（只做一次）
    BOOL hasChineseInNeedle = FALSE;
    BOOL hasEnglishInNeedle = FALSE;
    g_filterContext.hasNumbers = FALSE;

    // 安全的字符处理循环，确保不会越界
    for (int i = 0; i < needleLen && i < (MAX_FILTER_LENGTH - 1); i++)
    {
        WCHAR ch = needle[i];

        // 检查字符是否有效（避免处理损坏的Unicode字符）
        if (ch == 0)
        {
            break; // 遇到空字符，提前结束
        }

        if (ch >= 0x4E00 && ch <= 0x9FFF)
        {
            hasChineseInNeedle = TRUE;
            g_filterContext.patternLower[i] = ch; // 中文字符保持原样
        }
        else if ((ch >= L'A' && ch <= L'Z') || (ch >= L'a' && ch <= L'z'))
        {
            hasEnglishInNeedle = TRUE;
            // 预计算小写版本
            g_filterContext.patternLower[i] = (ch >= L'A' && ch <= L'Z') ? ch + 32 : ch;
        }
        else if (ch >= L'0' && ch <= L'9')
        {
            g_filterContext.hasNumbers = TRUE;
            g_filterContext.patternLower[i] = ch;
        }
        else
        {
            g_filterContext.patternLower[i] = ch;
        }
    }

    // 确保字符串正确终止
    if (needleLen < MAX_FILTER_LENGTH)
    {
        g_filterContext.patternLower[needleLen] = L'\0';
    }
    else
    {
        g_filterContext.patternLower[MAX_FILTER_LENGTH - 1] = L'\0';
    }

    // 确定筛选模式
    if (hasChineseInNeedle && !hasEnglishInNeedle)
    {
        g_filterContext.mode = FILTER_MODE_PURE_CHINESE;
    }
    else if (!hasChineseInNeedle && hasEnglishInNeedle)
    {
        g_filterContext.mode = FILTER_MODE_PURE_ENGLISH;
    }
    else if (hasChineseInNeedle && hasEnglishInNeedle)
    {
        g_filterContext.mode = FILTER_MODE_MIXED;
    }
    else
    {
        g_filterContext.mode = FILTER_MODE_GENERAL;
    }

    g_filterContextValid = TRUE;
}

// 高性能中文筛选优化函数（使用预处理上下文）
WCHAR *FindSubstringIgnoreCase(const WCHAR *haystack, const WCHAR *needle)
{
    if (!haystack || !needle)
        return NULL;

    // 安全检查字符串长度
    int haystackLen = wcslen(haystack);
    int needleLen = wcslen(needle);

    if (haystackLen == 0 || needleLen == 0 || needleLen > haystackLen)
        return needleLen == 0 ? (WCHAR *)haystack : NULL;

    // 安全的字符串比较：避免访问无效内存
    BOOL needContextUpdate = FALSE;
    if (!g_filterContextValid)
    {
        needContextUpdate = TRUE;
    }
    else
    {
        // 安全比较：确保两个字符串都有效
        int contextLen = wcslen(g_filterContext.pattern);
        if (contextLen != needleLen || wcsncmp(g_filterContext.pattern, needle, needleLen) != 0)
        {
            needContextUpdate = TRUE;
        }
    }

    if (needContextUpdate)
    {
        PrepareFilterContext(needle);
        if (!g_filterContextValid)
        {
            // 回退到简单的wcsstr搜索
            return wcsstr(haystack, needle);
        }
    }

    if (g_filterContext.patternLen > haystackLen)
        return NULL;

    if (g_filterContext.patternLen == 0)
        return (WCHAR *)haystack;

    // 根据预分析的模式选择最优算法
    switch (g_filterContext.mode)
    {
    case FILTER_MODE_PURE_CHINESE:
        // 纯中文：直接使用wcsstr，最快
        return wcsstr(haystack, needle);

    case FILTER_MODE_PURE_ENGLISH:
        // 纯英文：使用优化的忽略大小写搜索
        return FindEnglishSubstringIgnoreCase(haystack, needle);

    case FILTER_MODE_MIXED:
    case FILTER_MODE_GENERAL:
    default:
        // 混合模式：使用优化的逐字符匹配
        break;
    }

    // 混合字符快速搜索（优化版本）
    const WCHAR *pattern = g_filterContext.pattern;
    const WCHAR *patternLower = g_filterContext.patternLower;
    int patternLen = g_filterContext.patternLen;

    for (int i = 0; i <= haystackLen - patternLen; i++)
    {
        // 快速首字符匹配
        WCHAR h = haystack[i];
        WCHAR p = pattern[0];

        if (p >= 0x4E00 && p <= 0x9FFF)
        {
            // 中文首字符：精确匹配
            if (h != p)
                continue;
        }
        else if ((p >= L'A' && p <= L'Z') || (p >= L'a' && p <= L'z'))
        {
            // 英文首字符：忽略大小写
            WCHAR h_lower = (h >= L'A' && h <= L'Z') ? h + 32 : h;
            if (h_lower != patternLower[0])
                continue;
        }
        else
        {
            // 其他字符：精确匹配
            if (h != p)
                continue;
        }

        // 检查剩余字符
        BOOL match = TRUE;
        for (int j = 1; j < patternLen; j++)
        {
            WCHAR h_char = haystack[i + j];
            WCHAR p_char = pattern[j];

            if (p_char >= 0x4E00 && p_char <= 0x9FFF)
            {
                // 中文字符：精确匹配
                if (h_char != p_char)
                {
                    match = FALSE;
                    break;
                }
            }
            else if ((p_char >= L'A' && p_char <= L'Z') || (p_char >= L'a' && p_char <= L'z'))
            {
                // 英文字符：忽略大小写
                WCHAR h_lower = (h_char >= L'A' && h_char <= L'Z') ? h_char + 32 : h_char;
                if (h_lower != patternLower[j])
                {
                    match = FALSE;
                    break;
                }
            }
            else
            {
                // 其他字符：精确匹配
                if (h_char != p_char)
                {
                    match = FALSE;
                    break;
                }
            }
        }

        if (match)
        {
            return (WCHAR *)(haystack + i);
        }
    }

    return NULL;
}

// 纯英文字符串快速搜索（忽略大小写）
WCHAR *FindEnglishSubstringIgnoreCase(const WCHAR *haystack, const WCHAR *needle)
{
    if (!haystack || !needle)
        return NULL;

    int needleLen = wcslen(needle);
    if (needleLen == 0)
        return (WCHAR *)haystack;

    int haystackLen = wcslen(haystack);
    if (needleLen > haystackLen)
        return NULL;

    // 预处理：转换搜索字符串为小写（添加边界检查）
    WCHAR needleLower[MAX_FILTER_LENGTH];
    if (needleLen >= MAX_FILTER_LENGTH)
    {
        return NULL; // 搜索字符串太长
    }

    for (int i = 0; i < needleLen; i++)
    {
        WCHAR ch = needle[i];
        needleLower[i] = (ch >= L'A' && ch <= L'Z') ? ch + 32 : ch;
    }
    needleLower[needleLen] = L'\0';

    // 使用优化的Boyer-Moore算法进行快速搜索
    WCHAR firstChar = needleLower[0];

    for (int i = 0; i <= haystackLen - needleLen; i++)
    {
        WCHAR currentChar = haystack[i];
        WCHAR currentCharLower = (currentChar >= L'A' && currentChar <= L'Z') ? currentChar + 32 : currentChar;

        if (currentCharLower != firstChar)
            continue;

        // 检查完整匹配
        BOOL match = TRUE;
        for (int j = 1; j < needleLen; j++)
        {
            WCHAR c1 = haystack[i + j];
            WCHAR c1_lower = (c1 >= L'A' && c1 <= L'Z') ? c1 + 32 : c1;

            if (c1_lower != needleLower[j])
            {
                match = FALSE;
                break;
            }
        }

        if (match)
        {
            return (WCHAR *)(haystack + i);
        }
    }

    return NULL;
}

// 初始化DPI感知
void InitializeDPIAwareness()
{
    // 尝试设置Per-Monitor DPI Awareness V2 (Windows 10 1703+)
    typedef HRESULT(WINAPI * SetProcessDpiAwarenessContextProc)(DPI_AWARENESS_CONTEXT);
    HMODULE hUser32 = GetModuleHandleW(L"user32.dll");

    if (hUser32)
    {
        SetProcessDpiAwarenessContextProc SetProcessDpiAwarenessContextFunc =
            (SetProcessDpiAwarenessContextProc)GetProcAddress(hUser32, "SetProcessDpiAwarenessContext");

        if (SetProcessDpiAwarenessContextFunc)
        {
            HRESULT hr = SetProcessDpiAwarenessContextFunc(DPI_AWARENESS_CONTEXT_PER_MONITOR_AWARE_V2);
            if (SUCCEEDED(hr))
            {
                g_dpiAware = TRUE;
                return;
            }
        }
    }

    // 回退到Per-Monitor DPI Awareness V1 (Windows 8.1+)
    typedef HRESULT(WINAPI * SetProcessDpiAwarenessProc)(PROCESS_DPI_AWARENESS);
    HMODULE hShcore = LoadLibraryW(L"shcore.dll");

    if (hShcore)
    {
        SetProcessDpiAwarenessProc SetProcessDpiAwarenessFunc =
            (SetProcessDpiAwarenessProc)GetProcAddress(hShcore, "SetProcessDpiAwareness");

        if (SetProcessDpiAwarenessFunc)
        {
            HRESULT hr = SetProcessDpiAwarenessFunc(PROCESS_PER_MONITOR_DPI_AWARE);
            if (SUCCEEDED(hr))
            {
                g_dpiAware = TRUE;
                FreeLibrary(hShcore);
                return;
            }
        }
        FreeLibrary(hShcore);
    }

    // 最后回退到系统DPI感知 (Windows Vista+)
    if (SetProcessDPIAware())
    {
        g_dpiAware = TRUE;
    }
}

// 更新DPI设置
void UpdateDPISettings(HWND hwnd)
{
    if (!hwnd)
        return;

    // 尝试获取窗口的DPI (Windows 10 1607+)
    typedef UINT(WINAPI * GetDpiForWindowProc)(HWND);
    HMODULE hUser32 = GetModuleHandleW(L"user32.dll");

    if (hUser32)
    {
        GetDpiForWindowProc GetDpiForWindowFunc =
            (GetDpiForWindowProc)GetProcAddress(hUser32, "GetDpiForWindow");

        if (GetDpiForWindowFunc)
        {
            UINT dpi = GetDpiForWindowFunc(hwnd);
            g_dpiX = g_dpiY = dpi;
            g_dpiScaleX = g_dpiScaleY = (float)dpi / 96.0f;
            return;
        }
    }

    // 回退到系统DPI
    HDC hdc = GetDC(hwnd);
    if (hdc)
    {
        g_dpiX = GetDeviceCaps(hdc, LOGPIXELSX);
        g_dpiY = GetDeviceCaps(hdc, LOGPIXELSY);
        g_dpiScaleX = (float)g_dpiX / 96.0f;
        g_dpiScaleY = (float)g_dpiY / 96.0f;
        ReleaseDC(hwnd, hdc);
    }
}

// DPI缩放X坐标
int ScaleDPIX(int value)
{
    return (int)(value * g_dpiScaleX + 0.5f);
}

// DPI缩放Y坐标
int ScaleDPIY(int value)
{
    return (int)(value * g_dpiScaleY + 0.5f);
}

// 创建指定像素大小的字体
HFONT CreateScaledFont(int pixelSize, const WCHAR *fontName)
{
    // 直接使用像素大小，不进行DPI缩放
    // 负值表示字符高度（像素），正值表示字符单元高度
    LOGFONTW lf = {0};
    lf.lfHeight = -pixelSize; // 负值 = 像素高度
    lf.lfWeight = FW_NORMAL;
    lf.lfCharSet = DEFAULT_CHARSET;
    lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
    lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
    lf.lfQuality = CLEARTYPE_QUALITY;
    lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;

    if (fontName)
    {
        wcsncpy(lf.lfFaceName, fontName, LF_FACESIZE - 1);
    }
    else
    {
        wcscpy(lf.lfFaceName, L"Microsoft YaHei Light");
    }

    return CreateFontIndirectW(&lf);
}

// 创建指定像素大小的加粗字体
HFONT CreateScaledBoldFont(int pixelSize, const WCHAR *fontName)
{
    // 直接使用像素大小，不进行DPI缩放
    LOGFONTW lf = {0};
    lf.lfHeight = -pixelSize; // 负值 = 像素高度
    lf.lfWeight = FW_BOLD;    // 设置为加粗
    lf.lfCharSet = DEFAULT_CHARSET;
    lf.lfOutPrecision = OUT_DEFAULT_PRECIS;
    lf.lfClipPrecision = CLIP_DEFAULT_PRECIS;
    lf.lfQuality = CLEARTYPE_QUALITY;
    lf.lfPitchAndFamily = DEFAULT_PITCH | FF_DONTCARE;

    if (fontName)
    {
        wcsncpy(lf.lfFaceName, fontName, LF_FACESIZE - 1);
    }
    else
    {
        wcscpy(lf.lfFaceName, L"Microsoft YaHei");
    }

    return CreateFontIndirectW(&lf);
}

// 根据字体大小计算行高
int CalculateLineHeight(int fontSize)
{
    // 行高 = 字体大小 + 6px 间距，最小20px
    int lineHeight = fontSize + 6;
    if (lineHeight < 20)
        lineHeight = 20;
    return lineHeight;
}

// 更新输入框高度
void UpdateInputHeight()
{
    // 输入框高度 = 行高 + 4px 上下边距
    g_currentInputHeight = CalculateLineHeight(g_currentFontSize) + 4;

    // 重新布局所有控件
    if (hwndMain)
    {
        UpdateLayout(hwndMain);
    }
}

// 更新ListView行高
void UpdateListViewRowHeight()
{
    if (!hwndLeft)
        return;

    // 计算新的行高
    int newRowHeight = CalculateLineHeight(g_currentFontSize);

    // 方法1：通过ImageList设置行高
    HIMAGELIST hImageList = ListView_GetImageList(hwndLeft, LVSIL_SMALL);
    if (hImageList)
    {
        // 销毁旧的ImageList
        ImageList_Destroy(hImageList);
    }

    // 创建新的ImageList，高度匹配字体行高
    int iconHeight = max(16, newRowHeight - 6); // 确保图标高度合理
    HIMAGELIST hNewImageList = ImageList_Create(16, iconHeight, ILC_COLOR32 | ILC_MASK, 1, 1);
    if (hNewImageList)
    {
        // 创建一个透明图标来设置行高
        HDC hdc = GetDC(NULL);
        HDC hdcMem = CreateCompatibleDC(hdc);
        HBITMAP hBitmap = CreateCompatibleBitmap(hdc, 16, iconHeight);
        HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

        // 填充透明背景
        RECT rect = {0, 0, 16, iconHeight};
        FillRect(hdcMem, &rect, (HBRUSH)GetStockObject(NULL_BRUSH));

        SelectObject(hdcMem, hOldBitmap);
        DeleteDC(hdcMem);
        ReleaseDC(NULL, hdc);

        // 添加透明位图到ImageList
        ImageList_Add(hNewImageList, hBitmap, NULL);
        DeleteObject(hBitmap);

        // 设置新的ImageList
        ListView_SetImageList(hwndLeft, hNewImageList, LVSIL_SMALL);
    }

    // 方法2：强制ListView重新计算和重绘（保持滚动位置）
    if (g_virtualItemCount > 0)
    {
        // 保存当前滚动位置
        int topIndex = ListView_GetTopIndex(hwndLeft);

        // 临时清空再重新设置项目数量，强制重新计算行高
        ListView_SetItemCount(hwndLeft, 0);
        ListView_SetItemCount(hwndLeft, g_virtualItemCount);

        // 恢复滚动位置
        if (topIndex > 0 && topIndex < g_virtualItemCount)
        {
            ListView_EnsureVisible(hwndLeft, topIndex, FALSE);
        }
    }

    // 方法3：强制完全重绘
    InvalidateRect(hwndLeft, NULL, TRUE);
    UpdateWindow(hwndLeft);

    // 方法4：发送字体变化消息
    SendMessage(hwndLeft, WM_SETFONT, (WPARAM)(g_useBoldFont ? hFontBold : hFont), MAKELPARAM(TRUE, 0));
}

// 处理DPI变化消息
void HandleDPIChanged(HWND hwnd, WPARAM wParam, LPARAM lParam)
{
    // 获取新的DPI值
    UINT newDpiX = LOWORD(wParam);
    UINT newDpiY = HIWORD(wParam);

    // 更新DPI设置
    g_dpiX = newDpiX;
    g_dpiY = newDpiY;
    g_dpiScaleX = (float)newDpiX / 96.0f;
    g_dpiScaleY = (float)newDpiY / 96.0f;

    // 获取建议的窗口矩形
    RECT *prcNewWindow = (RECT *)lParam;
    if (prcNewWindow)
    {
        SetWindowPos(hwnd, NULL,
                     prcNewWindow->left, prcNewWindow->top,
                     prcNewWindow->right - prcNewWindow->left,
                     prcNewWindow->bottom - prcNewWindow->top,
                     SWP_NOZORDER | SWP_NOACTIVATE);
    }

    // 重新创建控件和字体
    PostMessage(hwnd, WM_USER + 10, 0, 0); // 自定义消息：重新布局
}

// 批量筛选优化函数
void PerformBatchFiltering(const WCHAR *filterText)
{
    if (!g_usnFiles || g_usnFileCount == 0 || !g_filterBitmap)
        return;

    DWORD startTime = GetTickCount();
    g_filteredCount = 0;

    // 优化批量处理大小（针对中文筛选优化）
    const int BATCH_SIZE = 2048; // 适中的批次大小

    // 预先分析筛选条件，优化匹配策略
    BOOL isSimpleFilter = (wcschr(filterText, L' ') == NULL);
    int filterLen = wcslen(filterText);

    // 详细分析筛选字符串的字符类型
    BOOL hasChineseFilter = FALSE;
    BOOL hasEnglishFilter = FALSE;
    BOOL hasNumberFilter = FALSE;
    BOOL hasSymbolFilter = FALSE;

    for (int i = 0; i < filterLen; i++)
    {
        WCHAR ch = filterText[i];
        if (ch >= 0x4E00 && ch <= 0x9FFF)
        {
            hasChineseFilter = TRUE;
        }
        else if ((ch >= L'A' && ch <= L'Z') || (ch >= L'a' && ch <= L'z'))
        {
            hasEnglishFilter = TRUE;
        }
        else if (ch >= L'0' && ch <= L'9')
        {
            hasNumberFilter = TRUE;
        }
        else if (ch != L' ')
        {
            hasSymbolFilter = TRUE;
        }
    }

    // 根据字符类型选择最优筛选策略
    int filterStrategy = 0; // 0=通用, 1=纯中文, 2=纯英文, 3=混合
    if (hasChineseFilter && !hasEnglishFilter)
        filterStrategy = 1; // 纯中文
    else if (!hasChineseFilter && hasEnglishFilter)
        filterStrategy = 2; // 纯英文
    else if (hasChineseFilter && hasEnglishFilter)
        filterStrategy = 3; // 混合

    for (int batchStart = 0; batchStart < g_usnFileCount; batchStart += BATCH_SIZE)
    {
        int batchEnd = min(batchStart + BATCH_SIZE, g_usnFileCount);

        // 批量处理当前批次
        for (int i = batchStart; i < batchEnd; i++)
        {
            BOOL matches = FALSE;

            // 根据筛选策略选择最优匹配方法
            if (isSimpleFilter)
            {
                switch (filterStrategy)
                {
                case 1: // 纯中文筛选
                    matches = (g_usnFiles[i].fileName && wcsstr(g_usnFiles[i].fileName, filterText) != NULL);
                    break;
                case 2: // 纯英文筛选
                    matches = (g_usnFiles[i].fileName && FindEnglishSubstringIgnoreCase(g_usnFiles[i].fileName, filterText) != NULL);
                    break;
                case 3: // 混合筛选
                    matches = (g_usnFiles[i].fileName && FindSubstringIgnoreCase(g_usnFiles[i].fileName, filterText) != NULL);
                    break;
                default: // 通用筛选
                    matches = (g_usnFiles[i].fileName && MatchesSingleKeyword(g_usnFiles[i].fileName, filterText));
                    break;
                }
            }
            else
            {
                // 复杂多关键词筛选
                matches = MatchesFilter(g_usnFiles[i].fileName, g_usnFiles[i].fullPath, filterText);
            }

            if (matches)
            {
                SetFilterBit(i, TRUE);
                g_filteredCount++;
            }
            else
            {
                SetFilterBit(i, FALSE);
            }
        }

        // 每处理一个批次检查是否需要中断
        if (!g_filterThreadRunning)
        {
            break;
        }

        // 定期报告进度（每处理10个批次）
        if ((batchStart / BATCH_SIZE) % 10 == 0)
        {
            DWORD elapsed = GetTickCount() - startTime;
            double rate = (elapsed > 0) ? (double)batchEnd / (elapsed / 1000.0) : 0;
        }
    }

    DWORD totalTime = GetTickCount() - startTime;
    double rate = (totalTime > 0) ? (double)g_usnFileCount / (totalTime / 1000.0) : 0;
}

// 优化的批量筛选函数（减少UI卡滞）
void PerformBatchFilteringNoLock(const WCHAR *filterText, int fileCount)
{
    if (!g_usnFiles || fileCount == 0 || !g_filterBitmap)
        return;

    DWORD startTime = GetTickCount();
    g_filteredCount = 0;

    // 调试输出
    WCHAR debugMsg[256];
    swprintf(debugMsg, 256, L"开始筛选: 文本=\"%s\", 文件数=%d", filterText, fileCount);
    OutputDebugStringW(debugMsg);

    // 预处理筛选上下文（避免重复分析）
    PrepareFilterContext(filterText);
    if (!g_filterContextValid)
    {
        // 如果上下文准备失败，使用简单的筛选方法
        OutputDebugStringW(L"筛选上下文准备失败，使用简单筛选方法");
        // 不要直接返回，继续执行筛选
    }

    // 根据文件数量动态调整批次大小
    int BATCH_SIZE;
    if (fileCount < 10000)
    {
        BATCH_SIZE = 2048; // 小数据集：较小批次，快速响应
    }
    else if (fileCount < 100000)
    {
        BATCH_SIZE = 8192; // 中等数据集：平衡性能和响应
    }
    else
    {
        BATCH_SIZE = 16384; // 大数据集：大批次，最大化性能
    }

    BOOL isSimpleFilter = (wcschr(filterText, L' ') == NULL);

    // 确定筛选策略（兼容上下文无效的情况）
    int filterStrategy = 0; // 0=通用, 1=纯中文, 2=纯英文, 3=混合
    if (g_filterContextValid)
    {
        // 使用预处理的上下文信息
        switch (g_filterContext.mode)
        {
        case FILTER_MODE_PURE_CHINESE:
            filterStrategy = 1;
            break;
        case FILTER_MODE_PURE_ENGLISH:
            filterStrategy = 2;
            break;
        case FILTER_MODE_MIXED:
            filterStrategy = 3;
            break;
        default:
            filterStrategy = 0;
            break;
        }
    }
    else
    {
        // 上下文无效时，动态分析筛选文本
        BOOL hasChineseFilter = FALSE;
        BOOL hasEnglishFilter = FALSE;

        for (int i = 0; filterText[i] != L'\0'; i++)
        {
            WCHAR ch = filterText[i];
            if (ch >= 0x4E00 && ch <= 0x9FFF)
            {
                hasChineseFilter = TRUE;
            }
            else if ((ch >= L'A' && ch <= L'Z') || (ch >= L'a' && ch <= L'z'))
            {
                hasEnglishFilter = TRUE;
            }
        }

        if (hasChineseFilter && !hasEnglishFilter)
            filterStrategy = 1; // 纯中文
        else if (!hasChineseFilter && hasEnglishFilter)
            filterStrategy = 2; // 纯英文
        else if (hasChineseFilter && hasEnglishFilter)
            filterStrategy = 3; // 混合
    }

    // 主筛选循环 - 内存友好的顺序访问
    int processedCount = 0;
    for (int batchStart = 0; batchStart < fileCount; batchStart += BATCH_SIZE)
    {
        int batchEnd = min(batchStart + BATCH_SIZE, fileCount);
        int batchMatches = 0;

        // 批量处理当前批次
        for (int i = batchStart; i < batchEnd; i++)
        {
            BOOL matches = FALSE;

            // 根据筛选策略选择最优匹配方法
            if (isSimpleFilter)
            {
                switch (filterStrategy)
                {
                case 1: // 纯中文筛选
                    matches = (g_usnFiles[i].fileName && wcsstr(g_usnFiles[i].fileName, filterText) != NULL);
                    break;
                case 2: // 纯英文筛选
                    matches = (g_usnFiles[i].fileName && FindEnglishSubstringIgnoreCase(g_usnFiles[i].fileName, filterText) != NULL);
                    break;
                case 3: // 混合筛选
                    matches = (g_usnFiles[i].fileName && FindSubstringIgnoreCase(g_usnFiles[i].fileName, filterText) != NULL);
                    break;
                default: // 通用筛选
                    matches = (g_usnFiles[i].fileName && MatchesSingleKeyword(g_usnFiles[i].fileName, filterText));
                    break;
                }
            }
            else
            {
                // 复杂多关键词筛选
                matches = MatchesFilter(g_usnFiles[i].fileName, g_usnFiles[i].fullPath, filterText);
            }

            // 内联位图操作，减少函数调用
            if (matches)
            {
                int byteIndex = i / 8;
                int bitIndex = i % 8;
                g_filterBitmap[byteIndex] |= (1 << bitIndex);
                batchMatches++;

                // 调试输出：记录前几个匹配的文件
                if (g_filteredCount + batchMatches <= 5)
                {
                    WCHAR debugMsg[256];
                    swprintf(debugMsg, 256, L"匹配文件 #%d: %s",
                             g_filteredCount + batchMatches, g_usnFiles[i].fileName);
                    OutputDebugStringW(debugMsg);
                }
            }
        }

        g_filteredCount += batchMatches;
        processedCount += (batchEnd - batchStart);

        // 检查中断请求和超时保护
        if (!g_filterThreadRunning)
        {
            break;
        }

        // 超时保护：如果筛选时间过长，强制退出
        DWORD elapsed = GetTickCount() - startTime;
        if (elapsed > 30000) // 30秒超时
        {
            OutputDebugStringW(L"筛选超时，强制退出");
            break;
        }

        // 更频繁的UI更新和中断检查
        if (processedCount % 10000 == 0 || batchStart + BATCH_SIZE >= fileCount)
        {
            // 异步通知UI更新（不阻塞筛选线程）
            if (hwndMain && IsWindow(hwndMain))
            {
                PostMessage(hwndMain, WM_USER + 7, processedCount, g_filteredCount);
            }

            // 检查中断请求
            if (!g_filterThreadRunning)
            {
                break;
            }
        }

        // 动态调整CPU让出策略
        if (fileCount > 50000)
        {
            // 大数据集：减少让出频率，提高吞吐量
            if ((batchStart / BATCH_SIZE) % 32 == 0)
            {
                Sleep(1); // 短暂让出
            }
        }
        else
        {
            // 小数据集：更频繁让出，保持响应性
            if ((batchStart / BATCH_SIZE) % 8 == 0)
            {
                Sleep(0);
            }
        }
    }

    DWORD totalTime = GetTickCount() - startTime;

    // 调试输出
    swprintf(debugMsg, 256, L"筛选完成: 找到 %d 个匹配文件，耗时 %d ms", g_filteredCount, totalTime);
    OutputDebugStringW(debugMsg);

    // 最终UI更新
    if (hwndMain && IsWindow(hwndMain))
    {
        PostMessage(hwndMain, WM_USER + 6, 0, 0); // 筛选完成
    }
}

// Update status bar with current filter info
void UpdateStatusBar()
{
    if (!hwndStatus)
        return;

    WCHAR statusText[512];
    double memoryMB = (g_usnFileCount * sizeof(USNFileData)) / (1024.0 * 1024.0);

    if (g_filterActive && wcslen(g_currentFilter) > 0)
    {
        if (g_currentLanguage == APP_LANG_CHINESE)
        {
            swprintf(statusText, 512, L"筛选：\"%s\" - 显示 %d / %d 个文件 (%.1fMB)",
                     g_currentFilter, g_filteredCount, g_usnFileCount, memoryMB);
        }
        else
        {
            swprintf(statusText, 512, L"Filter: \"%s\" - Showing %d / %d files (%.1fMB)",
                     g_currentFilter, g_filteredCount, g_usnFileCount, memoryMB);
        }
    }
    else
    {
        if (g_currentLanguage == APP_LANG_CHINESE)
        {
            swprintf(statusText, 512, L"文件预览器 - 共 %d 个文件 (%.1fMB)", g_usnFileCount, memoryMB);
        }
        else
        {
            swprintf(statusText, 512, L"File Previewer - %d files total (%.1fMB)", g_usnFileCount, memoryMB);
        }
    }

    SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)statusText);
}

// Apply filter to Virtual ListView - 直接在UI线程中进行简单筛选
void ApplyFilter(const WCHAR *filterText)
{
    WCHAR debugMsg[256];
    swprintf(debugMsg, 256, L"ApplyFilter 被调用: filterText='%s'", filterText ? filterText : L"(null)");
    OutputDebugStringW(debugMsg);

    // 检查数据加载状态和USN数据是否准备好
    if (!hwndLeft || !g_usnFiles || g_usnFileCount == 0 || !g_dataLoaded)
    {
        swprintf(debugMsg, 256, L"ApplyFilter 检查失败: hwndLeft=%p, usnFiles=%p, fileCount=%d, dataLoaded=%d",
                 hwndLeft, g_usnFiles, g_usnFileCount, g_dataLoaded);
        OutputDebugStringW(debugMsg);
        return;
    }

    // 暂时禁用复杂的线程化筛选，直接在UI线程中进行
    // 这样可以避免线程间的复杂交互问题

    const WCHAR *safeFilterText = filterText ? filterText : L"";

    if (safeFilterText[0] == L'\0')
    {
        // 清除筛选
        swprintf(debugMsg, 256, L"清除筛选，恢复显示所有文件");
        OutputDebugStringW(debugMsg);

        g_filterActive = FALSE;
        g_virtualItemCount = g_usnFileCount;

        // 直接更新ListView
        ListView_SetItemCount(hwndLeft, g_virtualItemCount);
        InvalidateRect(hwndLeft, NULL, TRUE);

        UpdateStatusBar();
    }
    else
    {
        // 进行简单的UI线程筛选
        swprintf(debugMsg, 256, L"开始UI线程筛选: '%s', 总文件数=%d", safeFilterText, g_usnFileCount);
        OutputDebugStringW(debugMsg);

        // 调试：显示前几个文件名
        for (int j = 0; j < min(5, g_usnFileCount); j++)
        {
            swprintf(debugMsg, 256, L"文件 #%d: %s", j, g_usnFiles[j].fileName);
            OutputDebugStringW(debugMsg);
        }

        // 初始化筛选位图
        if (!g_filterBitmap)
        {
            InitializeFilterBitmap();
        }

        if (g_filterBitmap)
        {
            // 清空筛选位图
            memset(g_filterBitmap, 0, g_bitmapSize);

            // 智能筛选：支持多关键词筛选（空格分隔）
            int matchCount = 0;

            // 检查是否包含空格（多关键词）
            BOOL isMultiKeyword = (wcschr(safeFilterText, L' ') != NULL);

            swprintf(debugMsg, 256, L"筛选模式: %s, 筛选文本: '%s'",
                     isMultiKeyword ? L"多关键词" : L"单关键词", safeFilterText);
            OutputDebugStringW(debugMsg);

            for (int i = 0; i < g_usnFileCount; i++) // 检查所有文件
            {
                if (g_usnFiles[i].fileName && g_usnFiles[i].fileName[0] != L'\0')
                {
                    BOOL matches = FALSE;

                    if (isMultiKeyword)
                    {
                        // 多关键词筛选：使用现有的 MatchesMultipleKeywords 函数
                        matches = MatchesMultipleKeywords(g_usnFiles[i].fileName, safeFilterText);
                    }
                    else
                    {
                        // 单关键词筛选：使用现有的 MatchesSingleKeyword 函数
                        matches = MatchesSingleKeyword(g_usnFiles[i].fileName, safeFilterText);
                    }

                    if (matches)
                    {
                        SetFilterBit(i, TRUE);
                        matchCount++;

                        // 调试：记录前几个匹配的文件
                        if (matchCount <= 5)
                        {
                            WCHAR debugMsg[256];
                            swprintf(debugMsg, 256, L"匹配文件 #%d: %s", matchCount, g_usnFiles[i].fileName);
                            OutputDebugStringW(debugMsg);
                        }
                    }
                }
            }

            // 更新筛选状态
            g_filterActive = TRUE;
            g_filteredCount = matchCount;
            g_virtualItemCount = matchCount > 0 ? matchCount : 1; // 至少显示1项

            // 保存当前筛选文本
            wcsncpy(g_currentFilter, safeFilterText, MAX_FILTER_LENGTH - 1);
            g_currentFilter[MAX_FILTER_LENGTH - 1] = L'\0';

            swprintf(debugMsg, 256, L"筛选完成: 匹配 %d 个文件, virtualItemCount=%d", matchCount, g_virtualItemCount);
            OutputDebugStringW(debugMsg);

            // 验证筛选位图
            int verifyCount = 0;
            for (int k = 0; k < min(100, g_usnFileCount); k++)
            {
                if (GetFilterBit(k))
                {
                    verifyCount++;
                    if (verifyCount <= 3)
                    {
                        swprintf(debugMsg, 256, L"验证匹配文件 #%d (索引%d): %s", verifyCount, k, g_usnFiles[k].fileName);
                        OutputDebugStringW(debugMsg);
                    }
                }
            }
            swprintf(debugMsg, 256, L"位图验证: 前100个文件中有 %d 个匹配", verifyCount);
            OutputDebugStringW(debugMsg);

            // 直接更新ListView
            ListView_SetItemCount(hwndLeft, g_virtualItemCount);
            InvalidateRect(hwndLeft, NULL, TRUE);

            UpdateStatusBar();
        }
        else
        {
            swprintf(debugMsg, 256, L"筛选位图初始化失败");
            OutputDebugStringW(debugMsg);
        }
    }

    swprintf(debugMsg, 256, L"ApplyFilter 完成");
    OutputDebugStringW(debugMsg);
}

// 防抖动相关变量
static UINT_PTR g_filterTimerId = 0;
static WCHAR g_pendingInputText[MAX_FILTER_LENGTH] = L"";

// 防抖动回调函数（已移除，改用WM_TIMER消息处理）

// 安全的文本清理函数
void SafeCleanFilterText(WCHAR *text, int maxLen)
{
    if (!text || maxLen <= 0)
        return;

    // 确保字符串正确终止
    text[maxLen - 1] = L'\0';

    // 移除可能导致问题的控制字符
    int len = wcslen(text);
    for (int i = 0; i < len; i++)
    {
        WCHAR ch = text[i];
        // 移除控制字符（除了空格）
        if (ch < 32 && ch != L' ')
        {
            // 将控制字符替换为空格或移除
            memmove(&text[i], &text[i + 1], (len - i) * sizeof(WCHAR));
            len--;
            i--; // 重新检查当前位置
        }
    }
}

// 极简的筛选处理机制
static DWORD g_lastFilterTime = 0;
static WCHAR g_currentFilterText[MAX_FILTER_LENGTH] = L"";

// Handle filter text change（安全版本，添加保护机制）
void OnFilterTextChanged()
{
    WCHAR debugMsg[256];
    swprintf(debugMsg, 256, L"OnFilterTextChanged 被调用");
    OutputDebugStringW(debugMsg);

    // 基本检查
    if (!hwndEdit || !g_dataLoaded)
    {
        swprintf(debugMsg, 256, L"基本检查失败: hwndEdit=%p, dataLoaded=%d", hwndEdit, g_dataLoaded);
        OutputDebugStringW(debugMsg);
        return;
    }

    // 获取当前文本
    WCHAR newText[MAX_FILTER_LENGTH];
    memset(newText, 0, sizeof(newText));

    int len = GetWindowTextW(hwndEdit, newText, MAX_FILTER_LENGTH - 1);
    if (len < 0)
        len = 0;
    newText[len] = L'\0';

    swprintf(debugMsg, 256, L"获取到筛选文本: '%s', 长度=%d", newText, len);
    OutputDebugStringW(debugMsg);

    // 如果文本为空，清除筛选，显示所有文件
    if (len == 0 || newText[0] == L'\0')
    {
        swprintf(debugMsg, 256, L"筛选文本为空，清除筛选显示所有文件");
        OutputDebugStringW(debugMsg);

        // 清除筛选，显示所有文件
        g_filterActive = FALSE;
        g_filteredCount = g_usnFileCount;
        g_virtualItemCount = g_usnFileCount;
        g_currentFilter[0] = L'\0';

        // 清除筛选位图
        if (g_filterBitmap && g_bitmapSize > 0)
        {
            memset(g_filterBitmap, 0xFF, g_bitmapSize); // 设置所有位为1，表示所有文件都显示
        }

        // 更新ListView
        ListView_SetItemCount(hwndLeft, g_virtualItemCount);
        InvalidateRect(hwndLeft, NULL, TRUE);
        UpdateStatusBar();
        return;
    }

    swprintf(debugMsg, 256, L"准备应用筛选: '%s'", newText);
    OutputDebugStringW(debugMsg);

    // 应用筛选
    ApplyFilter(newText);

    return;

    // 以下是原始代码，暂时注释掉
    /*
    // 记录当前时间
    DWORD currentTime = GetTickCount();

    // 防抖动：如果距离上次调用时间太短，直接返回
    if (currentTime - g_lastFilterTime < 50)
    {
        return;
    }

    g_lastFilterTime = currentTime;

    // 基本检查
    if (!hwndEdit || !g_dataLoaded)
    {
        return;
    }

    // 获取当前文本
    WCHAR newText[MAX_FILTER_LENGTH];
    memset(newText, 0, sizeof(newText));

    int len = GetWindowTextW(hwndEdit, newText, MAX_FILTER_LENGTH - 1);
    if (len < 0)
        len = 0;
    newText[len] = L'\0';

    // 如果文本没有变化，直接返回
    if (wcscmp(newText, g_currentFilterText) == 0)
    {
        return;
    }

    // 更新当前文本
    wcscpy(g_currentFilterText, newText);

    // 停止现有计时器
    if (g_filterTimerId != 0)
    {
        KillTimer(hwndMain, g_filterTimerId);
        g_filterTimerId = 0;
    }

    // 复制到待处理文本
    memset(g_pendingInputText, 0, sizeof(g_pendingInputText));
    wcscpy(g_pendingInputText, newText);

    // 设置新计时器
    g_filterTimerId = SetTimer(hwndMain, 1001, 200, NULL);

    // 如果计时器设置失败，直接应用筛选
    if (g_filterTimerId == 0)
    {
        ApplyFilter(newText);
    }
    */
}

// Render file tree from USN memory data
void RenderUSNFileTree()
{
    if (!hwndLeft || !g_usnFiles)
    {
        return;
    }

    if (!g_mainWindowReady)
    {
        return;
    }

    if (!IsWindow(hwndLeft))
    {
        return;
    }

    if (g_usnFileCount <= 0)
    {
        return;
    }

    // 使用虚拟模式设置项目数量
    EnterCriticalSection(&g_usnDataCriticalSection);

    ListView_SetItemCount(hwndLeft, g_usnFileCount);
    g_virtualItemCount = g_usnFileCount;
    g_dataLoaded = TRUE;

    LeaveCriticalSection(&g_usnDataCriticalSection);

    // 更新显示
    InvalidateRect(hwndLeft, NULL, FALSE);

    // 自动调整所有列宽度
    AutoAdjustAllColumnWidths();

    // Update status bar with final completion status
    if (hwndStatus)
    {
        WCHAR statusText[256];
        swprintf(statusText, 256, g_strings.loadComplete, g_usnFileCount);
        SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)statusText);
    }
}

// Handle Virtual ListView data requests - with USN data
LRESULT HandleVirtualListViewNotify(NMLVDISPINFOW *pDispInfo)
{
    if (!pDispInfo)
    {
        return 0;
    }

    // 调试输出：记录数据请求
    static int requestCount = 0;
    requestCount++;
    if (requestCount <= 100) // 增加记录次数以便调试
    {
        WCHAR debugMsg[256];
        swprintf(debugMsg, 256, L"ListView数据请求 #%d: item=%d, subItem=%d, mask=0x%x, filterActive=%d, virtualCount=%d, filteredCount=%d, filterInProgress=%d",
                 requestCount, pDispInfo->item.iItem, pDispInfo->item.iSubItem, pDispInfo->item.mask, g_filterActive, g_virtualItemCount, g_filteredCount, g_filterInProgress);
        OutputDebugStringW(debugMsg);
    }

    // 初始化返回值
    if (pDispInfo->item.mask & LVIF_TEXT && pDispInfo->item.pszText && pDispInfo->item.cchTextMax > 0)
    {
        pDispInfo->item.pszText[0] = L'\0';
    }

    // 检查基本数据有效性
    if (!g_usnFiles || !g_dataLoaded)
    {
        return 0;
    }

    int itemIndex = pDispInfo->item.iItem;

    // 检查索引有效性
    if (itemIndex < 0)
    {
        return 0;
    }

    // 快速获取必要的数据副本，减少锁定时间
    int actualIndex = itemIndex;
    BOOL filterActive = FALSE;
    int fileCount = 0;
    int virtualCount = 0;
    USNFileData localFileData = {0};

    // 使用TryEnterCriticalSection避免死锁
    if (!TryEnterCriticalSection(&g_dataCriticalSection))
    {
        // 如果无法获取锁，返回空数据而不是阻塞
        return 0;
    }

    // 快速复制必要的数据
    filterActive = g_filterActive;
    fileCount = g_usnFileCount;
    virtualCount = g_virtualItemCount;

    // 检查虚拟项目索引有效性
    if (itemIndex >= virtualCount)
    {
        LeaveCriticalSection(&g_dataCriticalSection);
        return 0;
    }

    // 简化的索引转换逻辑
    if (filterActive && g_filterBitmap && g_filteredCount > 0)
    {
        // 使用线性搜索找到第itemIndex个匹配的文件
        int count = 0;
        actualIndex = -1;

        for (int i = 0; i < fileCount && count <= itemIndex; i++)
        {
            if (GetFilterBit(i))
            {
                if (count == itemIndex)
                {
                    actualIndex = i;
                    break;
                }
                count++;
            }
        }

        // 调试输出前3个索引转换
        if (itemIndex < 3)
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"索引转换: virtual[%d] -> actual[%d], 文件名=%s",
                     itemIndex, actualIndex,
                     (actualIndex >= 0 && actualIndex < fileCount) ? g_usnFiles[actualIndex].fileName : L"INVALID");
            OutputDebugStringW(debugMsg);
        }

        // 如果转换失败，使用安全的默认值
        if (actualIndex == -1 || actualIndex >= fileCount)
        {
            actualIndex = 0; // 使用第一个文件作为安全默认值
        }
    }
    else
    {
        // 无筛选或筛选未激活：直接使用原始索引
        actualIndex = itemIndex;

        // 调试输出
        if (itemIndex < 3)
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"无筛选模式: itemIndex=%d, 使用原始索引=%d",
                     itemIndex, actualIndex);
            OutputDebugStringW(debugMsg);
        }
    }

    // 验证实际索引有效性
    if (actualIndex < 0 || actualIndex >= fileCount || !g_usnFiles)
    {
        // 调试输出：记录索引转换失败的情况
        if (itemIndex < 10) // 只记录前10个项目的错误，避免日志过多
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"**索引转换失败**: itemIndex=%d, actualIndex=%d, filterActive=%d, fileCount=%d, virtualCount=%d, filterInProgress=%d",
                     itemIndex, actualIndex, filterActive, fileCount, virtualCount, g_filterInProgress);
            OutputDebugStringW(debugMsg);

            // 额外调试：检查筛选位图状态
            if (filterActive && g_filterBitmap)
            {
                int matchCount = 0;
                for (int i = 0; i < min(fileCount, 100); i++) // 只检查前100个
                {
                    if (GetFilterBit(i))
                        matchCount++;
                }
                swprintf(debugMsg, 256, L"筛选位图检查: 前100个文件中有%d个匹配", matchCount);
                OutputDebugStringW(debugMsg);
            }
            else if (filterActive && !g_filterBitmap)
            {
                swprintf(debugMsg, 256, L"**关键问题**: filterActive=TRUE 但 g_filterBitmap=NULL!");
                OutputDebugStringW(debugMsg);
            }
        }
        LeaveCriticalSection(&g_dataCriticalSection);
        return 0;
    }

    // 复制文件数据
    localFileData = g_usnFiles[actualIndex];

    LeaveCriticalSection(&g_dataCriticalSection);

    // 现在使用本地数据副本进行处理，无需锁定
    // 验证文件数据有效性
    if (localFileData.fileName[0] == L'\0')
    {
        if (itemIndex < 5)
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"**文件数据为空**: itemIndex=%d, actualIndex=%d", itemIndex, actualIndex);
            OutputDebugStringW(debugMsg);
        }
        return 0;
    }

    // 调试：记录成功的数据请求
    if (itemIndex < 5)
    {
        WCHAR debugMsg[256];
        swprintf(debugMsg, 256, L"**数据请求成功**: itemIndex=%d, actualIndex=%d, fileName=%s",
                 itemIndex, actualIndex, localFileData.fileName);
        OutputDebugStringW(debugMsg);
    }

    // 设置文件图标
    if (pDispInfo->item.mask & LVIF_IMAGE && pDispInfo->item.iSubItem == 0)
    {
        // 使用缓存的图标索引获取正确的文件类型图标
        pDispInfo->item.iImage = GetCachedFileIconIndex(localFileData.fileName, localFileData.isDirectory);
    }

    if (pDispInfo->item.mask & LVIF_TEXT && pDispInfo->item.pszText && pDispInfo->item.cchTextMax > 0)
    {
        switch (pDispInfo->item.iSubItem)
        {
        case 0: // 文件名
            if (localFileData.fileName && localFileData.fileName[0] != L'\0')
            {
                wcsncpy(pDispInfo->item.pszText, localFileData.fileName,
                        pDispInfo->item.cchTextMax - 1);
                pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
            }
            else
            {
                wcscpy(pDispInfo->item.pszText, L"<No Name>");
            }
            break;
        case 1: // 路径 - 只显示目录路径
            if (localFileData.fullPath && localFileData.fullPath[0] != L'\0')
            {
                wcsncpy(pDispInfo->item.pszText, localFileData.fullPath,
                        pDispInfo->item.cchTextMax - 1);
                pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
            }
            else
            {
                wcscpy(pDispInfo->item.pszText, L"<No Path>");
            }
            break;
        case 2: // 大小 - 动态生成字符串
        {
            WCHAR sizeStr[16];

            // 检查是否为文件夹
            if (localFileData.isDirectory)
            {
                wcscpy(sizeStr, g_strings.folderSizeText);
            }
            else
            {
                DWORD fileSize = localFileData.fileSize;
                if (fileSize < 1024)
                    swprintf(sizeStr, 16, L"%lu B", fileSize);
                else if (fileSize < 1024 * 1024)
                    swprintf(sizeStr, 16, L"%.1fK", fileSize / 1024.0);
                else if (fileSize < 1024UL * 1024 * 1024)
                    swprintf(sizeStr, 16, L"%.1fM", fileSize / (1024.0 * 1024.0));
                else
                    swprintf(sizeStr, 16, L"%.1fG", fileSize / (1024.0 * 1024.0 * 1024.0));
            }

            wcsncpy(pDispInfo->item.pszText, sizeStr, pDispInfo->item.cchTextMax - 1);
            pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
        }
        break;
        case 3: // 修改时间 - 动态生成字符串
        {
            WCHAR timeStr[20];
            DWORD timestamp = localFileData.lastWriteTime;
            if (timestamp > 0)
            {
                // 直接使用Unix时间戳转换为本地时间
                time_t fileTime = (time_t)timestamp;
                struct tm *timeInfo = localtime(&fileTime);
                if (timeInfo)
                {
                    swprintf(timeStr, 20, L"%04d/%02d/%02d-%02d:%02d",
                             timeInfo->tm_year + 1900, timeInfo->tm_mon + 1, timeInfo->tm_mday,
                             timeInfo->tm_hour, timeInfo->tm_min);
                }
                else
                {
                    wcscpy(timeStr, L"时间错误");
                }
            }
            else
            {
                wcscpy(timeStr, L"未知");
            }

            wcsncpy(pDispInfo->item.pszText, timeStr, pDispInfo->item.cchTextMax - 1);
            pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
        }
        break;
        default:
            wcscpy(pDispInfo->item.pszText, L"");
            break;
        }
        pDispInfo->item.pszText[pDispInfo->item.cchTextMax - 1] = L'\0';
    }

    return 0;
}

// ListView子类化窗口过程，用于捕获滚动消息
LRESULT CALLBACK ListViewSubclassProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData)
{
    switch (uMsg)
    {
    case WM_VSCROLL:
    case WM_HSCROLL:
        // 标记正在滚动，优化图标渲染
        g_isScrolling = TRUE;
        g_lastScrollTime = GetTickCount();

        // 设置定时器延迟刷新和恢复图标显示
        if (hwndMain)
        {
            SetTimer(hwndMain, 1002, 200, NULL); // 200ms后恢复图标显示
        }
        break;

    case WM_MOUSEWHEEL:
        // 鼠标滚轮，标记滚动状态
        g_isScrolling = TRUE;
        g_lastScrollTime = GetTickCount();

        if (hwndMain)
        {
            SetTimer(hwndMain, 1002, 150, NULL); // 150ms后恢复图标显示
        }
        break;

    case WM_KEYDOWN:
        // 键盘滚动（Page Up/Down, 方向键等）
        if (wParam == VK_PRIOR || wParam == VK_NEXT ||
            wParam == VK_UP || wParam == VK_DOWN ||
            wParam == VK_HOME || wParam == VK_END)
        {
            g_isScrolling = TRUE;
            g_lastScrollTime = GetTickCount();

            if (hwndMain)
            {
                SetTimer(hwndMain, 1002, 100, NULL); // 100ms后恢复图标显示
            }
        }
        break;
    }

    return DefSubclassProc(hwnd, uMsg, wParam, lParam);
}

// 右侧预览区域子类化窗口过程，用于处理滚动时图片控件的显示
LRESULT CALLBACK PreviewSubclassProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam, UINT_PTR uIdSubclass, DWORD_PTR dwRefData)
{
    switch (uMsg)
    {
    case WM_VSCROLL:
    case WM_HSCROLL:
        // 滚动时不操作图片控件，避免重复创建问题
        // 图片控件位置由窗口大小改变时统一处理
        return DefSubclassProc(hwnd, uMsg, wParam, lParam);

    case WM_PAINT:
        // 正常处理绘制，不操作图片控件避免重复创建
        return DefSubclassProc(hwnd, uMsg, wParam, lParam);

    case WM_SIZE:
        // 窗口大小改变时重新定位图片控件 - 优化：由主布局统一处理
        {
            LRESULT result = DefSubclassProc(hwnd, uMsg, wParam, lParam);
            // 位置更新由主窗口的WM_SIZE处理，避免重复调用
            // if (IsImageControlValid())
            // {
            //     UpdateImagePosition(); // 移除重复调用
            // }
            return result;
        }
    }

    return DefSubclassProc(hwnd, uMsg, wParam, lParam);
}

// 强制刷新ListView显示，修复滚动时的空白问题（保持滚动位置）
void ForceRefreshListView()
{
    if (!hwndLeft || !IsWindow(hwndLeft) || g_virtualItemCount <= 0)
    {
        return;
    }

    // 保存当前滚动位置
    int topIndex = ListView_GetTopIndex(hwndLeft);

    // 暂停重绘以避免闪烁
    SendMessage(hwndLeft, WM_SETREDRAW, FALSE, 0);

    // 只刷新可见区域，不重置项目数量
    RECT clientRect;
    GetClientRect(hwndLeft, &clientRect);
    InvalidateRect(hwndLeft, &clientRect, FALSE);

    // 恢复重绘
    SendMessage(hwndLeft, WM_SETREDRAW, TRUE, 0);

    // 恢复滚动位置
    if (topIndex > 0 && topIndex < g_virtualItemCount)
    {
        ListView_EnsureVisible(hwndLeft, topIndex, FALSE);
    }

    UpdateWindow(hwndLeft);
}

// Create default configuration file
void CreateDefaultConfigFile()
{
    // 使用绝对路径来确保文件创建成功
    char fullPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, fullPath);
    strcat(fullPath, "\\");
    strcat(fullPath, CONFIG_FILE);

    // Write default window settings
    BOOL result1 = WritePrivateProfileStringA("Window", "X", "-1", fullPath); // CW_USEDEFAULT
    BOOL result2 = WritePrivateProfileStringA("Window", "Y", "-1", fullPath); // CW_USEDEFAULT

    char buffer[32];
    sprintf(buffer, "%d", DEFAULT_WINDOW_WIDTH);
    WritePrivateProfileStringA("Window", "Width", buffer, fullPath);

    sprintf(buffer, "%d", DEFAULT_WINDOW_HEIGHT);
    WritePrivateProfileStringA("Window", "Height", buffer, fullPath);

    WritePrivateProfileStringA("Window", "Maximized", "0", fullPath);

    // Write default splitter position
    WritePrivateProfileStringA("Splitter", "Position", "400", fullPath);

    // Write default column settings
    WritePrivateProfileStringA("Columns", "Width0", "200", fullPath); // 名称
    WritePrivateProfileStringA("Columns", "Width1", "300", fullPath); // 路径
    WritePrivateProfileStringA("Columns", "Width2", "100", fullPath); // 大小
    WritePrivateProfileStringA("Columns", "Width3", "150", fullPath); // 修改时间

    WritePrivateProfileStringA("Columns", "Order0", "0", fullPath); // 名称
    WritePrivateProfileStringA("Columns", "Order1", "1", fullPath); // 路径
    WritePrivateProfileStringA("Columns", "Order2", "2", fullPath); // 大小
    WritePrivateProfileStringA("Columns", "Order3", "3", fullPath); // 修改时间

    // Write default font settings
    WritePrivateProfileStringA("Font", "Size", "16", fullPath); // 默认16px字体
    WritePrivateProfileStringA("Font", "Bold", "0", fullPath);  // 默认正常字体

    // Add comments section for user reference
    WritePrivateProfileStringA("Comments", "Description", "File Previewer Configuration", fullPath);
    WritePrivateProfileStringA("Comments", "WindowX", "Window X position (-1 = default)", fullPath);
    WritePrivateProfileStringA("Comments", "WindowY", "Window Y position (-1 = default)", fullPath);
    WritePrivateProfileStringA("Comments", "SplitterPosition", "Splitter position in pixels", fullPath);
    WritePrivateProfileStringA("Comments", "ColumnWidths", "ListView column widths (Name, Path, Size, Modified)", fullPath);
    WritePrivateProfileStringA("Comments", "ColumnOrder", "ListView column display order (0-3)", fullPath);
    WritePrivateProfileStringA("Comments", "FontSize", "Font size in pixels (10-24px)", fullPath);
    WritePrivateProfileStringA("Comments", "FontBold", "Font style (0=normal, 1=bold)", fullPath);

    // Flush to disk
    WritePrivateProfileStringA(NULL, NULL, NULL, fullPath);
}

// Load configuration from INI file
void LoadConfiguration()
{

    // Set defaults
    g_config.windowX = CW_USEDEFAULT;
    g_config.windowY = CW_USEDEFAULT;
    g_config.windowWidth = DEFAULT_WINDOW_WIDTH;
    g_config.windowHeight = DEFAULT_WINDOW_HEIGHT;
    g_config.windowMaximized = FALSE;
    g_config.splitterPosition = 400;
    g_config.currentLanguage = APP_LANG_CHINESE;

    // Set default column settings
    g_config.columnWidths[0] = 200; // 名称
    g_config.columnWidths[1] = 300; // 路径
    g_config.columnWidths[2] = 100; // 大小
    g_config.columnWidths[3] = 150; // 修改时间

    g_config.columnOrder[0] = 0; // 名称
    g_config.columnOrder[1] = 1; // 路径
    g_config.columnOrder[2] = 2; // 大小
    g_config.columnOrder[3] = 3; // 修改时间

    // Set default font settings
    g_config.fontSize = 16;       // 默认16px字体
    g_config.useBoldFont = FALSE; // 默认使用正常字体

    // 使用绝对路径检查和读取配置文件
    char fullPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, fullPath);
    strcat(fullPath, "\\");
    strcat(fullPath, CONFIG_FILE);

    // Check if config file exists
    if (GetFileAttributesA(fullPath) == INVALID_FILE_ATTRIBUTES)
    {
        CreateDefaultConfigFile();
        return;
    }

    // Load settings from INI file
    g_config.windowX = GetPrivateProfileIntA("Window", "X", g_config.windowX, fullPath);
    g_config.windowY = GetPrivateProfileIntA("Window", "Y", g_config.windowY, fullPath);
    g_config.windowWidth = GetPrivateProfileIntA("Window", "Width", g_config.windowWidth, fullPath);
    g_config.windowHeight = GetPrivateProfileIntA("Window", "Height", g_config.windowHeight, fullPath);
    g_config.windowMaximized = GetPrivateProfileIntA("Window", "Maximized", g_config.windowMaximized, fullPath);
    g_config.splitterPosition = GetPrivateProfileIntA("Splitter", "Position", g_config.splitterPosition, fullPath);

    // Load column settings
    g_config.columnWidths[0] = GetPrivateProfileIntA("Columns", "Width0", g_config.columnWidths[0], fullPath);
    g_config.columnWidths[1] = GetPrivateProfileIntA("Columns", "Width1", g_config.columnWidths[1], fullPath);
    g_config.columnWidths[2] = GetPrivateProfileIntA("Columns", "Width2", g_config.columnWidths[2], fullPath);
    g_config.columnWidths[3] = GetPrivateProfileIntA("Columns", "Width3", g_config.columnWidths[3], fullPath);

    g_config.columnOrder[0] = GetPrivateProfileIntA("Columns", "Order0", g_config.columnOrder[0], fullPath);
    g_config.columnOrder[1] = GetPrivateProfileIntA("Columns", "Order1", g_config.columnOrder[1], fullPath);
    g_config.columnOrder[2] = GetPrivateProfileIntA("Columns", "Order2", g_config.columnOrder[2], fullPath);
    g_config.columnOrder[3] = GetPrivateProfileIntA("Columns", "Order3", g_config.columnOrder[3], fullPath);

    // Load language setting
    g_config.currentLanguage = (Language)GetPrivateProfileIntA("UI", "Language", APP_LANG_CHINESE, fullPath);
    g_currentLanguage = g_config.currentLanguage;

    // Load font settings
    g_config.fontSize = GetPrivateProfileIntA("Font", "Size", g_config.fontSize, fullPath);
    g_config.useBoldFont = GetPrivateProfileIntA("Font", "Bold", g_config.useBoldFont, fullPath);

    // Validate font size (must be between 10 and 24 px)
    if (g_config.fontSize < 10 || g_config.fontSize > 24)
    {
        g_config.fontSize = 16; // 默认16px字体
    }

    // Validate bold font setting
    if (g_config.useBoldFont != 0 && g_config.useBoldFont != 1)
    {
        g_config.useBoldFont = FALSE;
    }

    // Apply loaded font settings to global variables
    g_currentFontSize = g_config.fontSize;
    g_useBoldFont = g_config.useBoldFont;

    // 初始化输入框高度
    g_currentInputHeight = CalculateLineHeight(g_currentFontSize) + 4;
}

// Save configuration to INI file
void SaveConfiguration()
{
    if (!hwndMain)
        return;

    // 使用绝对路径来确保文件保存成功
    char fullPath[MAX_PATH];
    GetCurrentDirectoryA(MAX_PATH, fullPath);
    strcat(fullPath, "\\");
    strcat(fullPath, CONFIG_FILE);

    // Get current window position and size
    RECT rect;
    GetWindowRect(hwndMain, &rect);

    BOOL isMaximized = IsZoomed(hwndMain);

    if (!isMaximized)
    {
        g_config.windowX = rect.left;
        g_config.windowY = rect.top;
        g_config.windowWidth = rect.right - rect.left;
        g_config.windowHeight = rect.bottom - rect.top;
    }
    g_config.windowMaximized = isMaximized;
    g_config.splitterPosition = splitterPos;

    // Save to INI file
    char buffer[32];

    sprintf(buffer, "%d", g_config.windowX);
    WritePrivateProfileStringA("Window", "X", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowY);
    WritePrivateProfileStringA("Window", "Y", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowWidth);
    WritePrivateProfileStringA("Window", "Width", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowHeight);
    WritePrivateProfileStringA("Window", "Height", buffer, fullPath);

    sprintf(buffer, "%d", g_config.windowMaximized);
    WritePrivateProfileStringA("Window", "Maximized", buffer, fullPath);

    sprintf(buffer, "%d", g_config.splitterPosition);
    WritePrivateProfileStringA("Splitter", "Position", buffer, fullPath);

    // 扫描状态现在保存在独立文件中，不在这里保存

    // Save column settings
    sprintf(buffer, "%d", g_config.columnWidths[0]);
    WritePrivateProfileStringA("Columns", "Width0", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnWidths[1]);
    WritePrivateProfileStringA("Columns", "Width1", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnWidths[2]);
    WritePrivateProfileStringA("Columns", "Width2", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnWidths[3]);
    WritePrivateProfileStringA("Columns", "Width3", buffer, fullPath);

    sprintf(buffer, "%d", g_config.columnOrder[0]);
    WritePrivateProfileStringA("Columns", "Order0", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnOrder[1]);
    WritePrivateProfileStringA("Columns", "Order1", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnOrder[2]);
    WritePrivateProfileStringA("Columns", "Order2", buffer, fullPath);
    sprintf(buffer, "%d", g_config.columnOrder[3]);
    WritePrivateProfileStringA("Columns", "Order3", buffer, fullPath);

    // Save language setting
    sprintf(buffer, "%d", (int)g_config.currentLanguage);
    WritePrivateProfileStringA("UI", "Language", buffer, fullPath);

    // Save font settings
    sprintf(buffer, "%d", g_config.fontSize);
    WritePrivateProfileStringA("Font", "Size", buffer, fullPath);
    sprintf(buffer, "%d", g_config.useBoldFont);
    WritePrivateProfileStringA("Font", "Bold", buffer, fullPath);

    // Flush to disk
    WritePrivateProfileStringA(NULL, NULL, NULL, fullPath);
}

// Apply configuration to window
void ApplyConfiguration()
{
    if (!hwndMain)
        return;

    // Apply window position and size (only if not using default values)
    if (g_config.windowX > 0 && g_config.windowY > 0 &&
        g_config.windowWidth > 0 && g_config.windowHeight > 0)
    {
        SetWindowPos(hwndMain, NULL,
                     g_config.windowX, g_config.windowY,
                     g_config.windowWidth, g_config.windowHeight,
                     SWP_NOZORDER);
    }

    // Apply maximized state
    if (g_config.windowMaximized)
    {
        ShowWindow(hwndMain, SW_MAXIMIZE);
    }

    // Apply splitter position
    if (g_config.splitterPosition > 0)
    {
        splitterPos = g_config.splitterPosition;
        UpdateLayout(hwndMain);
    }
}

// 自动调整指定列的宽度
void AutoAdjustColumnWidth(int columnIndex)
{
    if (!hwndLeft || !g_usnFiles || g_usnFileCount == 0 || columnIndex < 0 || columnIndex > 3)
        return;

    HDC hdc = GetDC(hwndLeft);
    if (!hdc)
        return;

    // 获取当前字体
    HFONT hFont = (HFONT)SendMessage(hwndLeft, WM_GETFONT, 0, 0);
    HFONT hOldFont = NULL;
    if (hFont)
        hOldFont = (HFONT)SelectObject(hdc, hFont);

    int maxWidth = 0;
    int sampleCount = 0;
    int totalCount = 0;

    // 确定要处理的文件数量和范围
    if (g_filterActive && g_filteredCount > 0)
    {
        totalCount = g_filteredCount;
        sampleCount = min(1000, g_filteredCount); // 筛选模式下采样筛选后的文件
    }
    else
    {
        totalCount = g_usnFileCount;
        sampleCount = min(1000, g_usnFileCount); // 正常模式下采样所有文件
    }

    // 根据列索引计算不同内容的最大宽度
    int processedCount = 0;
    for (int i = 0; i < g_usnFileCount && processedCount < sampleCount; i++)
    {
        // 在筛选模式下，只处理筛选后的文件
        if (g_filterActive && !GetFilterBit(i))
        {
            continue; // 跳过未筛选的文件
        }

        WCHAR textBuffer[512] = L"";
        SIZE textSize;

        switch (columnIndex)
        {
        case 0: // 文件名列
            if (g_usnFiles[i].fileName && g_usnFiles[i].fileName[0] != L'\0')
            {
                wcscpy(textBuffer, g_usnFiles[i].fileName);
            }
            break;

        case 1: // 路径列 - 只使用目录路径
            if (g_usnFiles[i].fullPath && g_usnFiles[i].fullPath[0] != L'\0')
            {
                wcscpy(textBuffer, g_usnFiles[i].fullPath);
            }
            break;

        case 2: // 大小列
            if (!g_usnFiles[i].isDirectory)
            {
                LONGLONG fileSize = g_usnFiles[i].fileSize;
                if (fileSize < 1024)
                    swprintf(textBuffer, 512, L"%lldB", fileSize);
                else if (fileSize < 1024 * 1024)
                    swprintf(textBuffer, 512, L"%.1fK", fileSize / 1024.0);
                else if (fileSize < 1024 * 1024 * 1024)
                    swprintf(textBuffer, 512, L"%.1fM", fileSize / (1024.0 * 1024.0));
                else
                    swprintf(textBuffer, 512, L"%.1fG", fileSize / (1024.0 * 1024.0 * 1024.0));
            }
            else
            {
                wcscpy(textBuffer, L"<文件夹>");
            }
            break;

        case 3: // 修改时间列
        {
            DWORD timestamp = g_usnFiles[i].lastWriteTime;
            if (timestamp > 0)
            {
                time_t fileTime = (time_t)timestamp;
                struct tm *timeinfo = localtime(&fileTime);
                if (timeinfo)
                {
                    swprintf(textBuffer, 512, L"%04d-%02d-%02d %02d:%02d",
                             timeinfo->tm_year + 1900,
                             timeinfo->tm_mon + 1,
                             timeinfo->tm_mday,
                             timeinfo->tm_hour,
                             timeinfo->tm_min);
                }
            }
            else
            {
                wcscpy(textBuffer, L"未知");
            }
        }
        break;
        }

        if (textBuffer[0] != L'\0')
        {
            if (GetTextExtentPoint32W(hdc, textBuffer, wcslen(textBuffer), &textSize))
            {
                int width = textSize.cx + (columnIndex == 0 ? 40 : 20); // 文件名列添加图标空间
                if (width > maxWidth)
                    maxWidth = width;
            }
        }

        processedCount++; // 增加已处理文件计数
    }

    // 恢复原字体
    if (hOldFont)
        SelectObject(hdc, hOldFont);
    ReleaseDC(hwndLeft, hdc);

    // 根据列类型设置合理的最小和最大宽度
    int minWidth, maxAllowedWidth;
    switch (columnIndex)
    {
    case 0: // 文件名
        minWidth = 150;
        maxAllowedWidth = 400;
        break;
    case 1: // 路径
        minWidth = 200;
        maxAllowedWidth = 600;
        break;
    case 2: // 大小
        minWidth = 80;
        maxAllowedWidth = 120;
        break;
    case 3: // 修改时间
        minWidth = 130;
        maxAllowedWidth = 180;
        break;
    default:
        minWidth = 100;
        maxAllowedWidth = 300;
        break;
    }

    if (maxWidth < minWidth)
        maxWidth = minWidth;
    else if (maxWidth > maxAllowedWidth)
        maxWidth = maxAllowedWidth;

    // 应用新的列宽
    ListView_SetColumnWidth(hwndLeft, columnIndex, maxWidth);

    // 更新配置
    g_config.columnWidths[columnIndex] = maxWidth;
}

// 自动调整文件名列宽度（保持向后兼容）
void AutoAdjustNameColumnWidth()
{
    AutoAdjustColumnWidth(0);
}

// 自动调整所有列宽度
void AutoAdjustAllColumnWidths()
{
    for (int i = 0; i < 4; i++)
    {
        AutoAdjustColumnWidth(i);
    }
}

// Save column settings (widths and order)
void SaveColumnSettings()
{
    if (!hwndLeft)
        return;

    // Get current column widths
    for (int i = 0; i < 4; i++)
    {
        int width = ListView_GetColumnWidth(hwndLeft, i);
        if (width > 0)
        {
            // Map display column index to logical column index
            int logicalIndex = g_config.columnOrder[i];
            g_config.columnWidths[logicalIndex] = width;
        }
    }

    // Get current column order
    int order[4];
    if (ListView_GetColumnOrderArray(hwndLeft, 4, order))
    {
        // Update column order in configuration
        for (int i = 0; i < 4; i++)
        {
            g_config.columnOrder[i] = order[i];
        }
    }

    // Save configuration to file
    SaveConfiguration();
}

// 安全的字符串分配函数
WCHAR *SafeAllocateString(const WCHAR *source)
{
    if (!source)
        return NULL;

    size_t len = wcslen(source);
    if (len == 0)
        return NULL;

    WCHAR *result = (WCHAR *)malloc((len + 1) * sizeof(WCHAR));
    if (result)
    {
        wcscpy(result, source);
    }
    return result;
}

// USN Memory management functions
void InitializeUSNMemory()
{
    InitializeCriticalSection(&g_usnDataCriticalSection);
    g_usnFileCapacity = 4069000; // 直接分配最大容量，避免重新分配
    g_usnFiles = (USNFileData *)malloc(g_usnFileCapacity * sizeof(USNFileData));
    if (g_usnFiles)
    {
        // 初始化所有指针为NULL
        memset(g_usnFiles, 0, g_usnFileCapacity * sizeof(USNFileData));
    }
    g_usnFileCount = 0;
}

// 清理USN内存
void CleanupUSNMemory()
{
    EnterCriticalSection(&g_usnDataCriticalSection);

    // 释放每个文件记录的动态分配字符串
    if (g_usnFiles)
    {
        for (int i = 0; i < g_usnFileCount; i++)
        {
            if (g_usnFiles[i].fileName)
            {
                free(g_usnFiles[i].fileName);
                g_usnFiles[i].fileName = NULL;
            }
            if (g_usnFiles[i].fullPath)
            {
                free(g_usnFiles[i].fullPath);
                g_usnFiles[i].fullPath = NULL;
            }
        }

        // 释放主数组
        free(g_usnFiles);
        g_usnFiles = NULL;
    }

    g_usnFileCount = 0;
    g_usnFileCapacity = 0;

    LeaveCriticalSection(&g_usnDataCriticalSection);
    DeleteCriticalSection(&g_usnDataCriticalSection);
}

// 初始化筛选线程
void InitializeFilterThread()
{
    InitializeCriticalSection(&g_filterCriticalSection);
    g_filterThreadRunning = TRUE;
    g_filterPending = FALSE;

    g_filterThread = CreateThread(NULL, 0, FilterThreadProc, NULL, 0, &g_filterThreadId);
    if (g_filterThread == NULL)
    {
        g_filterThreadRunning = FALSE;
    }
}

// 清理筛选线程
void CleanupFilterThread()
{
    // 清理防抖动计时器
    if (g_filterTimerId != 0)
    {
        KillTimer(hwndMain, g_filterTimerId);
        g_filterTimerId = 0;
    }

    if (g_filterThreadRunning)
    {
        g_filterThreadRunning = FALSE;

        // 清除待处理的筛选请求，防止线程继续处理
        if (TryEnterCriticalSection(&g_filterCriticalSection))
        {
            g_filterPending = FALSE;
            g_pendingFilterText[0] = L'\0';
            LeaveCriticalSection(&g_filterCriticalSection);
        }

        if (g_filterThread)
        {
            WaitForSingleObject(g_filterThread, 5000); // 等待5秒
            CloseHandle(g_filterThread);
            g_filterThread = NULL;
        }
    }

    DeleteCriticalSection(&g_filterCriticalSection);
}

// 请求筛选操作（简化版本）
void RequestFilter(const WCHAR *filterText)
{
    // 基本检查
    if (!g_filterThreadRunning || !g_dataLoaded)
    {
        return;
    }

    // 在筛选开始前，先发送一个"筛选开始"消息，让UI保持当前状态
    if (hwndMain && IsWindow(hwndMain))
    {
        PostMessage(hwndMain, WM_USER + 9, 0, 0); // 新消息：筛选开始
    }

    // 快速更新筛选请求
    EnterCriticalSection(&g_filterCriticalSection);

    if (filterText && wcslen(filterText) > 0)
    {
        wcsncpy(g_pendingFilterText, filterText, 255);
        g_pendingFilterText[255] = L'\0';
    }
    else
    {
        g_pendingFilterText[0] = L'\0';
    }

    g_filterPending = TRUE;

    LeaveCriticalSection(&g_filterCriticalSection);
}

// 位图筛选辅助函数实现
void InitializeFilterBitmap()
{
    if (g_usnFileCapacity > 0)
    {
        g_bitmapSize = (g_usnFileCapacity + 7) / 8; // 每8个文件需要1字节
        g_filterBitmap = (BYTE *)calloc(g_bitmapSize, 1);
    }
}

void CleanupFilterBitmap()
{
    if (g_filterBitmap)
    {
        free(g_filterBitmap);
        g_filterBitmap = NULL;
        g_bitmapSize = 0;
    }

    // 清理筛选索引缓存
    if (g_filterIndexCache)
    {
        free(g_filterIndexCache);
        g_filterIndexCache = NULL;
        g_filterIndexCacheSize = 0;
        g_filterIndexCacheValid = FALSE;
    }
}

void SetFilterBit(int index, BOOL value)
{
    if (!g_filterBitmap || index < 0 || index >= g_usnFileCapacity)
        return;

    int byteIndex = index / 8;
    int bitIndex = index % 8;

    if (value)
        g_filterBitmap[byteIndex] |= (1 << bitIndex);
    else
        g_filterBitmap[byteIndex] &= ~(1 << bitIndex);
}

BOOL GetFilterBit(int index)
{
    if (!g_filterBitmap || index < 0 || index >= g_usnFileCapacity)
        return FALSE;

    int byteIndex = index / 8;
    int bitIndex = index % 8;

    return (g_filterBitmap[byteIndex] & (1 << bitIndex)) != 0;
}

int GetFilteredIndexByPosition(int position)
{
    if (!g_filterBitmap || position < 0)
    {
        if (position < 5) // 只记录前5个位置的错误
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"GetFilteredIndexByPosition失败: position=%d, bitmap=%p",
                     position, g_filterBitmap);
            OutputDebugStringW(debugMsg);
        }
        return -1;
    }

    // 如果缓存有效且位置在缓存范围内，直接返回缓存结果
    if (g_filterIndexCacheValid && position < g_filterIndexCacheSize && g_filterIndexCache)
    {
        return g_filterIndexCache[position];
    }

    // 简化策略：一次性构建完整缓存，确保正确性
    if (!g_filterIndexCacheValid || !g_filterIndexCache || g_filterIndexCacheSize < g_filteredCount)
    {
        // 检查筛选计数是否有效
        if (g_filteredCount <= 0)
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"筛选计数无效: g_filteredCount=%d", g_filteredCount);
            OutputDebugStringW(debugMsg);
            return -1;
        }

        // 重新分配缓存数组
        if (g_filterIndexCache)
        {
            free(g_filterIndexCache);
        }
        g_filterIndexCacheSize = g_filteredCount;
        g_filterIndexCache = (int *)malloc(g_filterIndexCacheSize * sizeof(int));
        if (!g_filterIndexCache)
        {
            g_filterIndexCacheSize = 0;
            g_filterIndexCacheValid = FALSE;
            // 回退到线性搜索
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"回退到线性搜索: position=%d, fileCount=%d", position, g_usnFileCount);
            OutputDebugStringW(debugMsg);

            int count = 0;
            for (int i = 0; i < g_usnFileCount; i++)
            {
                if (GetFilterBit(i))
                {
                    if (count == position)
                    {
                        swprintf(debugMsg, 256, L"线性搜索找到: position=%d -> index=%d", position, i);
                        OutputDebugStringW(debugMsg);
                        return i;
                    }
                    count++;
                }
            }

            swprintf(debugMsg, 256, L"线性搜索失败: position=%d, 总匹配数=%d", position, count);
            OutputDebugStringW(debugMsg);
            return -1;
        }

        // 构建完整缓存：使用优化的位图扫描
        int count = 0;
        for (int byteIndex = 0; byteIndex < g_bitmapSize && count < g_filterIndexCacheSize; byteIndex++)
        {
            BYTE currentByte = g_filterBitmap[byteIndex];
            if (currentByte == 0) // 整个字节都是0，跳过
            {
                continue;
            }

            // 检查这个字节中的每一位
            for (int bitIndex = 0; bitIndex < 8 && count < g_filterIndexCacheSize; bitIndex++)
            {
                int fileIndex = byteIndex * 8 + bitIndex;
                if (fileIndex >= g_usnFileCount)
                    break;

                if (currentByte & (1 << bitIndex))
                {
                    g_filterIndexCache[count] = fileIndex;
                    count++;
                }
            }
        }
        g_filterIndexCacheValid = TRUE;
    }

    // 返回请求的位置
    if (position < g_filterIndexCacheSize)
    {
        return g_filterIndexCache[position];
    }

    return -1;
}

// 使筛选索引缓存无效
void InvalidateFilterCache()
{
    g_filterIndexCacheValid = FALSE;
}

// 创建右键菜单
HMENU CreateFileContextMenu()
{
    HMENU hMenu = CreatePopupMenu();
    AppendMenuW(hMenu, MF_STRING, ID_OPEN_FOLDER, g_strings.menuOpenFolder);
    AppendMenuW(hMenu, MF_STRING, ID_OPEN_FILE, g_strings.menuOpenFile);
    AppendMenuW(hMenu, MF_SEPARATOR, 0, NULL);
    AppendMenuW(hMenu, MF_STRING, ID_COPY_PATH, g_strings.menuCopyPath);
    AppendMenuW(hMenu, MF_STRING, ID_PROPERTIES, g_strings.menuProperties);
    AppendMenuW(hMenu, MF_SEPARATOR, 0, NULL);

    // 添加语言子菜单
    HMENU hLangMenu = CreatePopupMenu();
    UINT chineseFlag = (g_currentLanguage == APP_LANG_CHINESE) ? MF_CHECKED : MF_UNCHECKED;
    UINT englishFlag = (g_currentLanguage == APP_LANG_ENGLISH) ? MF_CHECKED : MF_UNCHECKED;

    AppendMenuW(hLangMenu, MF_STRING | chineseFlag, ID_LANGUAGE_CHINESE, g_strings.menuChinese);
    AppendMenuW(hLangMenu, MF_STRING | englishFlag, ID_LANGUAGE_ENGLISH, g_strings.menuEnglish);

    AppendMenuW(hMenu, MF_POPUP, (UINT_PTR)hLangMenu, g_strings.menuLanguage);

    // 添加字体子菜单
    HMENU hFontMenu = CreatePopupMenu();
    UINT normalFlag = (!g_useBoldFont) ? MF_CHECKED : MF_UNCHECKED;
    UINT boldFlag = (g_useBoldFont) ? MF_CHECKED : MF_UNCHECKED;

    if (g_currentLanguage == APP_LANG_CHINESE)
    {
        AppendMenuW(hFontMenu, MF_STRING | normalFlag, ID_FONT_NORMAL, L"字体正常");
        AppendMenuW(hFontMenu, MF_STRING | boldFlag, ID_FONT_BOLD, L"字体加粗");
        AppendMenuW(hMenu, MF_POPUP, (UINT_PTR)hFontMenu, L"字体");
    }
    else
    {
        AppendMenuW(hFontMenu, MF_STRING | normalFlag, ID_FONT_NORMAL, L"Normal Font");
        AppendMenuW(hFontMenu, MF_STRING | boldFlag, ID_FONT_BOLD, L"Bold Font");
        AppendMenuW(hMenu, MF_POPUP, (UINT_PTR)hFontMenu, L"Font");
    }

    // 添加字体大小调整选项
    if (g_currentLanguage == APP_LANG_CHINESE)
    {
        AppendMenuW(hMenu, MF_STRING, ID_FONT_INCREASE, L"字体加大");
        AppendMenuW(hMenu, MF_STRING, ID_FONT_DECREASE, L"字体减小");
    }
    else
    {
        AppendMenuW(hMenu, MF_STRING, ID_FONT_INCREASE, L"Increase Font");
        AppendMenuW(hMenu, MF_STRING, ID_FONT_DECREASE, L"Decrease Font");
    }

    return hMenu;
}

// 获取实际文件索引（考虑筛选）
int GetActualFileIndex(int selectedIndex)
{
    if (g_filterActive && g_filterBitmap && g_filteredCount > 0)
    {
        // 使用与HandleVirtualListViewNotify相同的索引转换逻辑
        int count = 0;
        int actualIndex = -1;

        for (int i = 0; i < g_usnFileCount && count <= selectedIndex; i++)
        {
            if (GetFilterBit(i))
            {
                if (count == selectedIndex)
                {
                    actualIndex = i;
                    break;
                }
                count++;
            }
        }

        // 调试输出索引转换结果（仅前3个）
        if (selectedIndex < 3)
        {
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"GetActualFileIndex: selectedIndex=%d -> actualIndex=%d",
                     selectedIndex, actualIndex);
            OutputDebugStringW(debugMsg);
        }

        return actualIndex;
    }
    return selectedIndex;
}

// 构建完整文件路径
void BuildFullFilePath(int actualIndex, WCHAR *fullPath)
{
    if (actualIndex >= 0 && actualIndex < g_usnFileCount)
    {
        // 检查路径和文件名是否有效
        if (g_usnFiles[actualIndex].fullPath && g_usnFiles[actualIndex].fullPath[0] != L'\0' &&
            g_usnFiles[actualIndex].fileName && g_usnFiles[actualIndex].fileName[0] != L'\0')
        {
            // 检查路径是否以反斜杠结尾
            size_t pathLen = wcslen(g_usnFiles[actualIndex].fullPath);
            if (pathLen > 0 && g_usnFiles[actualIndex].fullPath[pathLen - 1] == L'\\')
            {
                // 路径已经以反斜杠结尾，直接连接
                swprintf(fullPath, MAX_PATH * 2, L"%s%s",
                         g_usnFiles[actualIndex].fullPath,
                         g_usnFiles[actualIndex].fileName);
            }
            else
            {
                // 路径不以反斜杠结尾，添加反斜杠
                swprintf(fullPath, MAX_PATH * 2, L"%s\\%s",
                         g_usnFiles[actualIndex].fullPath,
                         g_usnFiles[actualIndex].fileName);
            }
        }
        else if (g_usnFiles[actualIndex].fileName)
        {
            wcscpy(fullPath, g_usnFiles[actualIndex].fileName);
        }
        else
        {
            fullPath[0] = L'\0';
        }
    }
    else
    {
        fullPath[0] = L'\0';
    }
}

// 构建完整文件夹路径
void BuildFullFolderPath(int actualIndex, WCHAR *fullPath)
{
    if (actualIndex >= 0 && actualIndex < g_usnFileCount)
    {
        // 对于文件夹，fullPath已经存储了完整路径
        if (g_usnFiles[actualIndex].fullPath && g_usnFiles[actualIndex].fullPath[0] != L'\0')
        {
            // 直接使用存储的完整路径
            wcscpy(fullPath, g_usnFiles[actualIndex].fullPath);
        }
        else if (g_usnFiles[actualIndex].fileName)
        {
            // 如果只有文件名，可能是根目录下的文件夹
            wcscpy(fullPath, g_usnFiles[actualIndex].fileName);
        }
        else
        {
            fullPath[0] = L'\0';
        }
    }
    else
    {
        fullPath[0] = L'\0';
    }
}

// 打开文件夹功能
void OpenFileFolder(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        MessageBoxW(hwndMain, L"文件路径为空。", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 调试输出：显示要打开的文件路径
    WCHAR debugMsg[MAX_PATH * 2 + 50];
    swprintf(debugMsg, MAX_PATH * 2 + 50, L"尝试打开文件夹: %s", filePath);
    OutputDebugStringW(debugMsg);

    // 检查文件是否存在
    DWORD fileAttributes = GetFileAttributesW(filePath);
    if (fileAttributes == INVALID_FILE_ATTRIBUTES)
    {
        // 文件不存在，尝试只打开目录
        WCHAR dirPath[MAX_PATH * 2];
        wcscpy(dirPath, filePath);

        // 找到最后一个反斜杠，获取目录路径
        WCHAR *lastSlash = wcsrchr(dirPath, L'\\');
        if (lastSlash != NULL)
        {
            *lastSlash = L'\0'; // 截断，只保留目录路径

            // 检查目录是否存在
            DWORD dirAttributes = GetFileAttributesW(dirPath);
            if (dirAttributes != INVALID_FILE_ATTRIBUTES && (dirAttributes & FILE_ATTRIBUTE_DIRECTORY))
            {
                // 目录存在，直接打开目录
                WCHAR command[MAX_PATH * 3];
                swprintf(command, MAX_PATH * 3, L"explorer.exe \"%s\"", dirPath);

                STARTUPINFO si = {0};
                PROCESS_INFORMATION pi = {0};
                si.cb = sizeof(si);

                CreateProcess(NULL, command, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi);
                if (pi.hProcess)
                    CloseHandle(pi.hProcess);
                if (pi.hThread)
                    CloseHandle(pi.hThread);
                return;
            }
        }

        // 如果目录也不存在，显示错误消息
        MessageBoxW(hwndMain, L"文件或目录不存在，无法打开。", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    // 文件存在，使用explorer.exe /select参数打开文件夹并选中文件
    WCHAR command[MAX_PATH * 3];
    swprintf(command, MAX_PATH * 3, L"explorer.exe /select,\"%s\"", filePath);

    STARTUPINFO si = {0};
    PROCESS_INFORMATION pi = {0};
    si.cb = sizeof(si);

    if (CreateProcess(NULL, command, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi))
    {
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    }
    else
    {
        // 如果CreateProcess失败，尝试直接打开目录
        WCHAR dirPath[MAX_PATH * 2];
        wcscpy(dirPath, filePath);

        WCHAR *lastSlash = wcsrchr(dirPath, L'\\');
        if (lastSlash != NULL)
        {
            *lastSlash = L'\0';

            WCHAR fallbackCommand[MAX_PATH * 3];
            swprintf(fallbackCommand, MAX_PATH * 3, L"explorer.exe \"%s\"", dirPath);

            STARTUPINFO fallbackSi = {0};
            PROCESS_INFORMATION fallbackPi = {0};
            fallbackSi.cb = sizeof(fallbackSi);

            CreateProcess(NULL, fallbackCommand, NULL, NULL, FALSE, 0, NULL, NULL, &fallbackSi, &fallbackPi);
            if (fallbackPi.hProcess)
                CloseHandle(fallbackPi.hProcess);
            if (fallbackPi.hThread)
                CloseHandle(fallbackPi.hThread);
        }
    }
}

// 打开文件功能
void OpenFileWithDefaultApp(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    // 调试输出：显示要打开的文件路径
    WCHAR debugMsg[MAX_PATH * 2 + 50];
    swprintf(debugMsg, MAX_PATH * 2 + 50, L"尝试打开文件: %s", filePath);
    OutputDebugStringW(debugMsg);

    // 检查文件是否存在
    DWORD fileAttributes = GetFileAttributesW(filePath);
    if (fileAttributes == INVALID_FILE_ATTRIBUTES)
    {
        MessageBoxW(hwndMain, L"文件不存在或无法访问。", L"错误", MB_OK | MB_ICONERROR);
        return;
    }

    HINSTANCE result = ShellExecute(NULL, L"open", filePath, NULL, NULL, SW_SHOWNORMAL);

    if ((INT_PTR)result <= 32)
    {
        // 打开失败，显示错误信息
        WCHAR errorMsg[512];
        swprintf(errorMsg, 512, L"无法打开文件: %s\n错误代码: %d", filePath, (int)(INT_PTR)result);
        MessageBoxW(hwndMain, errorMsg, L"错误", MB_OK | MB_ICONERROR);
    }
}

// 复制路径到剪贴板
void CopyPathToClipboard(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    if (OpenClipboard(hwndMain))
    {
        EmptyClipboard();

        int len = (wcslen(filePath) + 1) * sizeof(WCHAR);
        HGLOBAL hMem = GlobalAlloc(GMEM_MOVEABLE, len);

        if (hMem)
        {
            WCHAR *pMem = (WCHAR *)GlobalLock(hMem);
            wcscpy(pMem, filePath);
            GlobalUnlock(hMem);

            SetClipboardData(CF_UNICODETEXT, hMem);
        }

        CloseClipboard();
    }
}

// 显示文件属性
void ShowFileProperties(const WCHAR *filePath)
{
    if (!filePath || wcslen(filePath) == 0)
    {
        return;
    }

    SHELLEXECUTEINFO sei = {0};
    sei.cbSize = sizeof(sei);
    sei.fMask = SEE_MASK_INVOKEIDLIST;
    sei.lpVerb = L"properties";
    sei.lpFile = filePath;
    sei.nShow = SW_SHOWNORMAL;

    ShellExecuteEx(&sei);
}

// 处理菜单命令
void HandleMenuCommand(int cmd, const WCHAR *filePath)
{
    switch (cmd)
    {
    case ID_OPEN_FOLDER:
        OpenFileFolder(filePath);
        break;

    case ID_OPEN_FILE:
        OpenFileWithDefaultApp(filePath);
        break;

    case ID_COPY_PATH:
        CopyPathToClipboard(filePath);
        break;

    case ID_PROPERTIES:
        ShowFileProperties(filePath);
        break;

    case ID_LANGUAGE_CHINESE:
        SetLanguage(APP_LANG_CHINESE);
        break;

    case ID_LANGUAGE_ENGLISH:
        SetLanguage(APP_LANG_ENGLISH);
        break;

    case ID_FONT_NORMAL:
        SetFontStyle(FALSE); // 使用正常字体 (微软雅黑Light)
        break;

    case ID_FONT_BOLD:
        SetFontStyle(TRUE); // 使用加粗字体 (微软雅黑)
        break;

    case ID_FONT_INCREASE:
        if (g_currentFontSize < 24) // 最大24px
        {
            SetFontSize(g_currentFontSize + 2); // 每次增加2px
        }
        break;

    case ID_FONT_DECREASE:
        if (g_currentFontSize > 10) // 最小10px
        {
            SetFontSize(g_currentFontSize - 2); // 每次减少2px
        }
        break;

    default:
        break;
    }
}

// 显示右键菜单
void ShowContextMenu(POINT pt, const WCHAR *filePath)
{
    // 转换为屏幕坐标
    ClientToScreen(hwndLeft, &pt);

    // 创建菜单
    HMENU hMenu = CreateFileContextMenu();

    // 显示菜单并获取用户选择
    int cmd = TrackPopupMenu(hMenu,
                             TPM_RETURNCMD | TPM_RIGHTBUTTON,
                             pt.x, pt.y, 0, hwndMain, NULL);

    // 处理菜单选择
    if (cmd > 0)
    {
        HandleMenuCommand(cmd, filePath);
    }

    // 清理菜单
    DestroyMenu(hMenu);
}

// 处理ListView右键点击
void HandleListViewRightClick(LPNMITEMACTIVATE pnmia)
{
    // 获取点击位置的文件索引
    int selectedIndex = pnmia->iItem;
    if (selectedIndex == -1)
    {
        return;
    }

    // 获取实际文件索引（考虑筛选）
    int actualIndex = GetActualFileIndex(selectedIndex);
    if (actualIndex == -1)
    {
        return;
    }

    // 构建完整路径（根据是否为文件夹选择不同的构建方式）
    WCHAR fullPath[MAX_PATH * 2];
    if (g_usnFiles[actualIndex].isDirectory)
    {
        BuildFullFolderPath(actualIndex, fullPath);
    }
    else
    {
        BuildFullFilePath(actualIndex, fullPath);
    }

    if (wcslen(fullPath) == 0)
    {
        return;
    }

    // 显示右键菜单
    ShowContextMenu(pnmia->ptAction, fullPath);
}

// 处理ListView双击事件
void HandleListViewDoubleClick(LPNMITEMACTIVATE pnmia)
{
    // 调试输出
    OutputDebugStringW(L"ListView双击事件触发");

    // 获取双击位置的文件索引
    int selectedIndex = pnmia->iItem;
    if (selectedIndex == -1)
    {
        OutputDebugStringW(L"双击事件：未选中任何项目");
        return;
    }

    // 获取实际文件索引（考虑筛选）
    int actualIndex = GetActualFileIndex(selectedIndex);
    if (actualIndex == -1)
    {
        OutputDebugStringW(L"双击事件：无法获取实际文件索引");
        return;
    }

    // 调试输出索引信息和文件信息
    WCHAR debugMsg[512];
    swprintf(debugMsg, 512, L"双击事件：selectedIndex=%d, actualIndex=%d, fileName=%s, isDirectory=%d",
             selectedIndex, actualIndex,
             g_usnFiles[actualIndex].fileName ? g_usnFiles[actualIndex].fileName : L"NULL",
             g_usnFiles[actualIndex].isDirectory);
    OutputDebugStringW(debugMsg);

    // 检查索引有效性
    if (actualIndex >= 0 && actualIndex < g_usnFileCount)
    {
        // 构建完整路径
        WCHAR fullPath[MAX_PATH * 2];

        if (g_usnFiles[actualIndex].isDirectory)
        {
            // 对于文件夹，构建文件夹的完整路径
            BuildFullFolderPath(actualIndex, fullPath);

            if (wcslen(fullPath) == 0)
            {
                OutputDebugStringW(L"双击事件：文件夹路径信息不完整");
                MessageBoxW(hwndMain, L"文件夹路径信息不完整。", L"错误", MB_OK | MB_ICONERROR);
                return;
            }

            swprintf(debugMsg, 512, L"双击事件：打开文件夹 - %s", fullPath);
            OutputDebugStringW(debugMsg);

            // 使用explorer打开文件夹
            HINSTANCE result = ShellExecuteW(NULL, L"explore", fullPath, NULL, NULL, SW_SHOWNORMAL);
            if ((INT_PTR)result <= 32)
            {
                swprintf(debugMsg, 512, L"打开文件夹失败: %s (错误代码: %d)", fullPath, (int)(INT_PTR)result);
                OutputDebugStringW(debugMsg);

                // 尝试使用open动作
                result = ShellExecuteW(NULL, L"open", fullPath, NULL, NULL, SW_SHOWNORMAL);
                if ((INT_PTR)result <= 32)
                {
                    MessageBoxW(hwndMain, L"无法打开文件夹。", L"错误", MB_OK | MB_ICONERROR);
                }
            }
        }
        else
        {
            // 对于文件，构建文件的完整路径
            BuildFullFilePath(actualIndex, fullPath);

            if (wcslen(fullPath) == 0)
            {
                OutputDebugStringW(L"双击事件：无法构建文件路径");
                return;
            }

            swprintf(debugMsg, 512, L"双击事件：打开文件 - %s", fullPath);
            OutputDebugStringW(debugMsg);

            OpenFileWithDefaultApp(fullPath);
        }
    }
}

// 简化的筛选线程主函数
DWORD WINAPI FilterThreadProc(LPVOID lpParam)
{
    while (g_filterThreadRunning)
    {
        BOOL needFilter = FALSE;
        WCHAR localFilterText[256] = L"";

        // 检查是否有新筛选请求
        EnterCriticalSection(&g_filterCriticalSection);

        if (g_filterPending)
        {
            wcscpy(localFilterText, g_pendingFilterText);
            g_filterPending = FALSE;
            needFilter = TRUE;
        }

        LeaveCriticalSection(&g_filterCriticalSection);

        if (needFilter && g_filterThreadRunning)
        {
            // 执行筛选操作
            PerformFilterOperation(localFilterText);
        }

        // 休眠一段时间
        Sleep(10);
    }
    return 0;
}

// 执行实际的筛选操作（简化版本）
void PerformFilterOperation(const WCHAR *filterText)
{
    // 基本检查
    if (!g_usnFiles || g_usnFileCount == 0 || !g_dataLoaded)
    {
        return;
    }

    const WCHAR *safeFilterText = filterText ? filterText : L"";

    // 获取数据锁并执行筛选
    EnterCriticalSection(&g_dataCriticalSection);

    // 设置筛选状态
    if (safeFilterText[0] != L'\0')
    {
        // 标记筛选开始
        g_filterInProgress = TRUE;

        // 在筛选过程中，完全不修改任何显示相关的全局变量
        // 包括 g_filterActive, g_virtualItemCount, g_filteredCount
        // 只更新筛选文本
        wcscpy(g_currentFilter, safeFilterText);

        // 使用局部变量进行筛选，完全不影响当前全局状态
        BYTE *tempBitmap = NULL;
        int tempFilteredCount = 0;

        if (g_filterBitmap)
        {
            // 创建临时位图进行筛选
            tempBitmap = (BYTE *)calloc(g_bitmapSize, 1);
            if (tempBitmap)
            {
                // 备份原始状态
                BYTE *originalBitmap = g_filterBitmap;
                int originalFilteredCount = g_filteredCount;

                // 使用临时变量进行筛选
                g_filterBitmap = tempBitmap;
                g_filteredCount = 0; // 临时重置计数器

                PrepareFilterContext(safeFilterText);
                PerformBatchFilteringNoLock(safeFilterText, g_usnFileCount);

                // 保存筛选结果
                tempFilteredCount = g_filteredCount;

                // 筛选完成后，只更新数据状态，不修改显示状态
                free(originalBitmap); // 释放原位图
                g_filteredCount = tempFilteredCount;
                g_filterActive = TRUE;
                g_filterIndexCacheValid = FALSE;

                // 不要在这里修改 g_virtualItemCount！
                // 让UI更新消息来处理显示状态的更新
            }
        }

        // 标记筛选完成
        g_filterInProgress = FALSE;

        // 调试输出：确认筛选结果
        WCHAR debugMsg[256];
        swprintf(debugMsg, 256, L"筛选完成设置: virtualCount=%d, filteredCount=%d, filterActive=%d",
                 g_virtualItemCount, g_filteredCount, g_filterActive);
        OutputDebugStringW(debugMsg);
    }
    else
    {
        // 清除筛选 - 但不要在筛选线程中修改 g_virtualItemCount
        g_currentFilter[0] = L'\0';
        g_filterActive = FALSE;
        // 不要在这里修改 g_virtualItemCount！让UI线程来处理
        g_filteredCount = g_usnFileCount; // 确保筛选计数与总数一致
        g_filterIndexCacheValid = FALSE;
        g_filterInProgress = FALSE; // 清除筛选进行标志

        if (g_filterBitmap)
        {
            memset(g_filterBitmap, 0xFF, g_bitmapSize);
        }
    }

    // 更新缓存
    wcscpy(g_lastFilterText, safeFilterText);
    g_filterCacheValid = TRUE;

    // 通知主线程更新UI
    if (hwndMain && IsWindow(hwndMain))
    {
        PostMessage(hwndMain, WM_USER + 6, 0, 0); // 自定义消息：筛选完成
    }
}

void AddUSNFile(const WCHAR *fileName, const WCHAR *fullPath, LONGLONG fileSize, FILETIME lastWriteTime, DWORD fileAttributes)
{
    // 快速检查容量限制，避免临界区
    if (g_usnFileCount >= g_usnFileCapacity)
    {
        return; // 已达到预分配容量限制
    }

    EnterCriticalSection(&g_usnDataCriticalSection);

    // 再次检查（双重检查锁定模式）
    if (g_usnFileCount >= g_usnFileCapacity)
    {
        LeaveCriticalSection(&g_usnDataCriticalSection);
        return;
    }

    // Add file data
    USNFileData *file = &g_usnFiles[g_usnFileCount];

    // 动态分配并复制文件名（完整长度）
    file->fileName = SafeAllocateString(fileName);

    // 动态分配并复制路径（完整长度）
    if (fullPath && wcslen(fullPath) > 0)
    {
        WCHAR tempPath[MAX_PATH * 2];
        wcsncpy(tempPath, fullPath, MAX_PATH * 2 - 1);
        tempPath[MAX_PATH * 2 - 1] = L'\0';

        // 对于文件夹，保存完整路径；对于文件，保存目录路径
        if (fileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        {
            // 文件夹：保存完整路径
            file->fullPath = SafeAllocateString(tempPath);
        }
        else
        {
            // 文件：找到最后一个反斜杠，截取目录部分
            WCHAR *lastSlash = wcsrchr(tempPath, L'\\');
            if (lastSlash != NULL)
            {
                *lastSlash = L'\0'; // 截断，只保留目录路径
            }
            file->fullPath = SafeAllocateString(tempPath);
        }
    }
    else
    {
        file->fullPath = NULL;
    }

    // 压缩文件大小（限制为4GB）
    file->fileSize = (DWORD)(fileSize > 0xFFFFFFFF ? 0xFFFFFFFF : fileSize);

    // 转换FILETIME为Unix时间戳（32位）
    ULARGE_INTEGER ull;
    ull.LowPart = lastWriteTime.dwLowDateTime;
    ull.HighPart = lastWriteTime.dwHighDateTime;

    // 转换为Unix时间戳（秒）
    ULONGLONG unixTime = (ull.QuadPart - 116444736000000000ULL) / 10000000ULL;
    file->lastWriteTime = (DWORD)(unixTime > 0xFFFFFFFF ? 0xFFFFFFFF : unixTime);

    // 设置是否为文件夹
    file->isDirectory = (fileAttributes & FILE_ATTRIBUTE_DIRECTORY) ? 1 : 0;

    // 移除预计算字符串，改为动态生成以节省内存

    g_usnFileCount++;

    LeaveCriticalSection(&g_usnDataCriticalSection);

    // 在1000个文件时进行一次早期渲染，让用户尽快看到内容
    if (g_usnFileCount == 1000)
    {
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 5, 0, 0);
        }
    }

    // 更频繁地更新进度显示，但不更新文件列表
    if (g_usnFileCount % 50000 == 0)
    {
        if (hwndStatus && IsWindow(hwndStatus))
        {
            WCHAR progressText[256];
            // 显示文件数量和内存使用情况
            double memoryMB = (g_usnFileCount * sizeof(USNFileData)) / (1024.0 * 1024.0);
            swprintf(progressText, 256, L"已扫描: %d 文件 (%.1fMB内存)", g_usnFileCount, memoryMB);
            SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)progressText);
        }
    }

    // 减少文件列表更新频率
    if (g_usnFileCount % 500000 == 0)
    {
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 5, 0, 0);
        }
    }
}

// USN读取线程 - 简化版本，只读取文件名、路径、大小、修改时间
DWORD WINAPI USNReadThreadProc(LPVOID lpParam)
{
    g_usnReadInProgress = TRUE;

    // 在状态栏显示开始扫描信息
    if (hwndStatus && IsWindow(hwndStatus))
    {
        SetStatusBarText(g_strings.statusScanning);
    }

    // Update status bar
    if (hwndStatus)
    {
        SetStatusBarText(g_strings.statusScanning);
    }

    // 调用高性能优化版本（最快的扫描速度）
    int result = ReadUSNToMemoryOptimized();

    // 如果优化版本失败，回退到双线程版本
    if (result <= 0 && g_usnFileCount == 0)
    {
        result = ReadUSNToMemoryDualThreaded();
    }

    // 如果双线程也失败，回退到多线程版本
    if (result <= 0 && g_usnFileCount == 0)
    {
        result = ReadUSNToMemoryMultiThreaded();
    }

    // 检查是否成功扫描到文件
    if (g_usnFileCount > 0)
    {
        // Update status bar with completion info
        if (hwndStatus)
        {
            WCHAR statusText[256];
            swprintf(statusText, 256, g_strings.scanComplete, g_usnFileCount);
            SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)statusText);
        }

        // 触发文件树渲染
        if (hwndMain && IsWindow(hwndMain))
        {
            PostMessage(hwndMain, WM_USER + 5, 0, 0); // 触发渲染
        }
    }
    else
    {
        // 没有找到文件
        if (hwndStatus)
        {
            if (g_currentLanguage == APP_LANG_CHINESE)
            {
                SetStatusBarText(L"未找到文件");
            }
            else
            {
                SetStatusBarText(L"No files found");
            }
        }
    }

    g_usnReadInProgress = FALSE;
    return result;
}

// 启动USN读取线程
void StartUSNReadThread()
{
    if (g_usnReadInProgress)
        return;

    g_usnReadThread = CreateThread(NULL, 0, USNReadThreadProc, NULL, 0, NULL);
}

// File monitoring thread
DWORD WINAPI FileWatchThreadProc(LPVOID lpParam)
{
    g_fileWatchActive = TRUE;

    HANDLE hDir = CreateFileW(
        L"C:\\",
        FILE_LIST_DIRECTORY,
        FILE_SHARE_READ | FILE_SHARE_WRITE | FILE_SHARE_DELETE,
        NULL,
        OPEN_EXISTING,
        FILE_FLAG_BACKUP_SEMANTICS,
        NULL);

    if (hDir == INVALID_HANDLE_VALUE)
    {
        g_fileWatchActive = FALSE;
        return 1;
    }

    BYTE buffer[4096];
    DWORD bytesReturned;

    while (g_fileWatchActive)
    {
        if (ReadDirectoryChangesW(
                hDir,
                buffer,
                sizeof(buffer),
                TRUE, // Watch subdirectories
                FILE_NOTIFY_CHANGE_FILE_NAME | FILE_NOTIFY_CHANGE_SIZE | FILE_NOTIFY_CHANGE_LAST_WRITE,
                &bytesReturned,
                NULL,
                NULL))
        {
            FILE_NOTIFY_INFORMATION *pNotify = (FILE_NOTIFY_INFORMATION *)buffer;

            do
            {
                WCHAR fileName[MAX_PATH];
                wcsncpy(fileName, pNotify->FileName, pNotify->FileNameLength / sizeof(WCHAR));
                fileName[pNotify->FileNameLength / sizeof(WCHAR)] = L'\0';

                switch (pNotify->Action)
                {
                case FILE_ACTION_ADDED:
                    break;
                case FILE_ACTION_REMOVED:
                    break;
                case FILE_ACTION_MODIFIED:
                    break;
                }

                // Move to next notification
                if (pNotify->NextEntryOffset == 0)
                    break;
                pNotify = (FILE_NOTIFY_INFORMATION *)((BYTE *)pNotify + pNotify->NextEntryOffset);

            } while (TRUE);

            // Refresh UI periodically
            PostMessage(hwndMain, WM_USER + 2, 0, 0);
        }
        else
        {
            DWORD error = GetLastError();
            if (error != ERROR_OPERATION_ABORTED)
            {
                // 监控错误
            }
            break;
        }
    }

    CloseHandle(hDir);
    g_fileWatchActive = FALSE;
    return 0;
}

// Start file watcher
void StartFileWatcher()
{
    if (g_fileWatchActive)
        return;

    g_fileWatchThread = CreateThread(NULL, 0, FileWatchThreadProc, NULL, 0, NULL);
}

// Stop file watcher
void StopFileWatcher()
{
    if (!g_fileWatchActive)
        return;

    g_fileWatchActive = FALSE;

    if (g_fileWatchThread)
    {
        WaitForSingleObject(g_fileWatchThread, 3000);
        CloseHandle(g_fileWatchThread);
        g_fileWatchThread = NULL;
    }
}

// Check if file is a text file
BOOL IsTextFile(const WCHAR *filePath)
{
    if (!filePath)
        return FALSE;

    const WCHAR *ext = wcsrchr(filePath, L'.');
    if (!ext)
        return FALSE;

    const WCHAR *textExts[] = {
        L".txt", L".log", L".ini", L".cfg", L".conf", L".xml", L".json",
        L".html", L".htm", L".css", L".js", L".c", L".cpp", L".h", L".hpp",
        L".py", L".java", L".cs", L".php", L".sql", L".md", L".readme"};

    for (int i = 0; i < sizeof(textExts) / sizeof(textExts[0]); i++)
    {
        if (_wcsicmp(ext, textExts[i]) == 0)
            return TRUE;
    }

    return FALSE;
}

// Check if file is an image file
BOOL IsImageFile(const WCHAR *filePath)
{
    if (!filePath)
        return FALSE;

    const WCHAR *ext = wcsrchr(filePath, L'.');
    if (!ext)
        return FALSE;

    const WCHAR *imageExts[] = {
        L".jpg", L".jpeg", L".png", L".gif", L".bmp", L".ico", L".tiff", L".tif"};

    for (int i = 0; i < sizeof(imageExts) / sizeof(imageExts[0]); i++)
    {
        if (_wcsicmp(ext, imageExts[i]) == 0)
            return TRUE;
    }

    return FALSE;
}

// Load file preview content
void LoadFilePreview(const WCHAR *filePath)
{
    if (!hwndRight || !filePath)
        return;

    // Check if file exists
    if (GetFileAttributesW(filePath) == INVALID_FILE_ATTRIBUTES)
    {
        SetControlTextUTF8(hwndRight, g_strings.fileNotFound);
        return;
    }

    // Get file info
    WIN32_FIND_DATAW findData;
    HANDLE hFind = FindFirstFileW(filePath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
    {
        SetControlTextUTF8(hwndRight, g_strings.fileInfoError);
        return;
    }
    FindClose(hFind);

    // Format file size
    WCHAR sizeStr[64];
    LARGE_INTEGER fileSize;
    fileSize.LowPart = findData.nFileSizeLow;
    fileSize.HighPart = findData.nFileSizeHigh;

    if (fileSize.QuadPart < 1024)
        swprintf(sizeStr, 64, L"%lld bytes", fileSize.QuadPart);
    else if (fileSize.QuadPart < 1024 * 1024)
        swprintf(sizeStr, 64, L"%.1f KB", fileSize.QuadPart / 1024.0);
    else if (fileSize.QuadPart < 1024 * 1024 * 1024)
        swprintf(sizeStr, 64, L"%.1f MB", fileSize.QuadPart / (1024.0 * 1024.0));
    else
        swprintf(sizeStr, 64, L"%.1f GB", fileSize.QuadPart / (1024.0 * 1024.0 * 1024.0));

    // Format modification time (格式: 2024-07-25 07:18)
    WCHAR timeStr[64];
    SYSTEMTIME st;
    FileTimeToSystemTime(&findData.ftLastWriteTime, &st);
    swprintf(timeStr, 64, L"%04d-%02d-%02d %02d:%02d",
             st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute);

    // Build preview content
    WCHAR previewContent[8192];
    swprintf(previewContent, 8192,
             g_strings.previewTemplate,
             wcsrchr(filePath, L'\\') ? wcsrchr(filePath, L'\\') + 1 : filePath,
             filePath,
             sizeStr,
             timeStr);

    // Try to load content based on file type
    if (IsTextFile(filePath) && fileSize.QuadPart < 1024 * 1024) // Max 1MB for text preview
    {
        wcscat(previewContent, g_strings.fileContent);

        HANDLE hFile = CreateFileW(filePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
        if (hFile != INVALID_HANDLE_VALUE)
        {
            DWORD fileSize = GetFileSize(hFile, NULL);
            if (fileSize > 0 && fileSize < 4096) // Limit preview size
            {
                char *buffer = (char *)malloc(fileSize + 1);
                if (buffer)
                {
                    DWORD bytesRead;
                    if (ReadFile(hFile, buffer, fileSize, &bytesRead, NULL))
                    {
                        buffer[bytesRead] = '\0';

                        // Convert to wide characters
                        WCHAR *wideBuffer = (WCHAR *)malloc((bytesRead + 1) * sizeof(WCHAR));
                        if (wideBuffer)
                        {
                            MultiByteToWideChar(CP_UTF8, 0, buffer, -1, wideBuffer, bytesRead + 1);
                            wcscat(previewContent, wideBuffer);
                            free(wideBuffer);
                        }
                    }
                    free(buffer);
                }
            }
            else
            {
                wcscat(previewContent, g_strings.fileTooLarge);
            }
            CloseHandle(hFile);
        }
        else
        {
            wcscat(previewContent, g_strings.cannotReadFile);
        }
    }
    else if (IsImageFile(filePath))
    {
        wcscat(previewContent, g_strings.imageFileInfo);
    }
    else
    {
        wcscat(previewContent, g_strings.binaryFileInfo);
    }

    // Set preview content
    SetControlTextUTF8(hwndRight, previewContent);
}

// Load README.md file content for initial preview
void LoadReadmePreview()
{
    if (!hwndRight)
        return;

    // 构建README.md文件的完整路径
    WCHAR readmePath[MAX_PATH];
    GetModuleFileNameW(NULL, readmePath, MAX_PATH);

    // 获取程序所在目录
    WCHAR *lastSlash = wcsrchr(readmePath, L'\\');
    if (lastSlash)
    {
        *(lastSlash + 1) = L'\0'; // 保留最后的反斜杠
        wcscat(readmePath, L"README.md");
    }
    else
    {
        wcscpy(readmePath, L"README.md"); // 如果获取路径失败，使用相对路径
    }

    // 检查README.md文件是否存在
    if (GetFileAttributesW(readmePath) == INVALID_FILE_ATTRIBUTES)
    {
        // 如果程序目录没有README.md，尝试当前工作目录
        wcscpy(readmePath, L"README.md");
        if (GetFileAttributesW(readmePath) == INVALID_FILE_ATTRIBUTES)
        {
            // 如果都没有找到，显示默认欢迎信息
            WCHAR welcomeText[2048];
            swprintf(welcomeText, 2048,
                     L"欢迎使用 EveryView 文件浏览器！\r\n\r\n"
                     L"🚀 主要特性：\r\n"
                     L"• 超快速文件索引 - 基于USN Journal技术\r\n"
                     L"• 海量文件支持 - 支持140万+文件\r\n"
                     L"• 实时筛选搜索 - 支持中文、英文、混合搜索\r\n"
                     L"• 文件预览功能 - 支持多种文件格式\r\n"
                     L"• 双击打开文件 - 直接用默认程序打开\r\n\r\n"
                     L"📖 使用方法：\r\n"
                     L"1. 在搜索框中输入关键词进行筛选\r\n"
                     L"2. 点击文件查看详细信息和预览\r\n"
                     L"3. 双击文件用默认程序打开\r\n"
                     L"4. 右键文件查看更多操作选项\r\n\r\n"
                     L"💡 提示：程序正在后台加载文件索引，请稍候...\r\n\r\n"
                     L"注：未找到README.md文件，显示默认欢迎信息。");
            SetControlTextUTF8(hwndRight, welcomeText);
            return;
        }
    }

    // 专门为README.md加载完整内容（不受大小限制）
    HANDLE hFile = CreateFileW(readmePath, GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING, 0, NULL);
    if (hFile == INVALID_HANDLE_VALUE)
    {
        SetControlTextUTF8(hwndRight, L"无法打开README.md文件。");
        return;
    }

    // 获取文件大小
    LARGE_INTEGER fileSize;
    if (!GetFileSizeEx(hFile, &fileSize))
    {
        CloseHandle(hFile);
        SetControlTextUTF8(hwndRight, L"无法获取README.md文件大小。");
        return;
    }

    // 限制最大文件大小为1MB，防止内存问题
    if (fileSize.QuadPart > 1024 * 1024)
    {
        CloseHandle(hFile);
        SetControlTextUTF8(hwndRight, L"README.md文件过大，无法完整显示。");
        return;
    }

    // 分配内存读取整个文件
    DWORD fileSizeDW = (DWORD)fileSize.QuadPart;
    char *buffer = (char *)malloc(fileSizeDW + 1);
    if (!buffer)
    {
        CloseHandle(hFile);
        SetControlTextUTF8(hwndRight, L"内存不足，无法加载README.md。");
        return;
    }

    // 读取文件内容
    DWORD bytesRead;
    if (!ReadFile(hFile, buffer, fileSizeDW, &bytesRead, NULL))
    {
        free(buffer);
        CloseHandle(hFile);
        SetControlTextUTF8(hwndRight, L"读取README.md文件失败。");
        return;
    }
    CloseHandle(hFile);

    buffer[bytesRead] = '\0';

    // 转换为宽字符
    int wideCharCount = MultiByteToWideChar(CP_UTF8, 0, buffer, -1, NULL, 0);
    if (wideCharCount == 0)
    {
        free(buffer);
        SetControlTextUTF8(hwndRight, L"README.md文件编码转换失败。");
        return;
    }

    WCHAR *wideBuffer = (WCHAR *)malloc(wideCharCount * sizeof(WCHAR));
    if (!wideBuffer)
    {
        free(buffer);
        SetControlTextUTF8(hwndRight, L"内存不足，无法转换README.md内容。");
        return;
    }

    if (MultiByteToWideChar(CP_UTF8, 0, buffer, -1, wideBuffer, wideCharCount) == 0)
    {
        free(buffer);
        free(wideBuffer);
        SetControlTextUTF8(hwndRight, L"README.md文件编码转换失败。");
        return;
    }

    // 创建完整的预览内容
    WCHAR *previewContent = (WCHAR *)malloc((wideCharCount + 8192) * sizeof(WCHAR));
    if (!previewContent)
    {
        free(buffer);
        free(wideBuffer);
        SetControlTextUTF8(hwndRight, L"内存不足，无法显示README.md内容。");
        return;
    }

    // 处理Markdown格式，保持原有排版
    // 简单的Markdown到文本转换，保持结构清晰
    // 增加额外空间用于Tesla标志和格式化
    WCHAR *formattedContent = (WCHAR *)malloc((wideCharCount * 2 + 4096) * sizeof(WCHAR));
    if (!formattedContent)
    {
        free(buffer);
        free(wideBuffer);
        SetControlTextUTF8(hwndRight, L"内存不足，无法格式化README.md内容。");
        return;
    }

    // 逐行处理，保持Markdown的视觉结构
    WCHAR *context = NULL;
    WCHAR *line = wcstok(wideBuffer, L"\n", &context);
    formattedContent[0] = L'\0';

    while (line != NULL)
    {
        // 移除行末的\r字符
        int len = wcslen(line);
        if (len > 0 && line[len - 1] == L'\r')
            line[len - 1] = L'\0';

        // 处理不同的Markdown元素
        if (wcsstr(line, L"# ") == line)
        {
            // 一级标题 - 添加装饰
            wcscat(formattedContent, L"═══════════════════════════════════════\r\n");
            wcscat(formattedContent, line + 2); // 移除 "# "
            wcscat(formattedContent, L"\r\n═══════════════════════════════════════\r\n\r\n");
        }
        else if (wcsstr(line, L"## ") == line)
        {
            // 二级标题
            wcscat(formattedContent, L"\r\n▶ ");
            wcscat(formattedContent, line + 3); // 移除 "## "
            wcscat(formattedContent, L"\r\n───────────────────────────────────────\r\n");
        }
        else if (wcsstr(line, L"### ") == line)
        {
            // 三级标题
            wcscat(formattedContent, L"\r\n● ");
            wcscat(formattedContent, line + 4); // 移除 "### "
            wcscat(formattedContent, L"\r\n");
        }
        else if (wcsstr(line, L"#### ") == line)
        {
            // 四级标题
            wcscat(formattedContent, L"\r\n◆ ");
            wcscat(formattedContent, line + 5); // 移除 "#### "
            wcscat(formattedContent, L"\r\n");
        }
        else if (wcsstr(line, L"- ") == line || wcsstr(line, L"* ") == line)
        {
            // 列表项
            wcscat(formattedContent, L"  • ");
            wcscat(formattedContent, line + 2);
            wcscat(formattedContent, L"\r\n");
        }
        else if (wcsstr(line, L"```") == line)
        {
            // 代码块标记
            if (wcslen(line) > 3)
            {
                wcscat(formattedContent, L"┌─ 代码示例 (");
                wcscat(formattedContent, line + 3);
                wcscat(formattedContent, L") ─────────────────────┐\r\n");
            }
            else
            {
                wcscat(formattedContent, L"└─────────────────────────────────────┘\r\n");
            }
        }
        else if (wcsstr(line, L"---") == line)
        {
            // 分隔线
            wcscat(formattedContent, L"\r\n═══════════════════════════════════════\r\n\r\n");
        }
        else if (wcsstr(line, L"> ") == line)
        {
            // 引用块
            wcscat(formattedContent, L"│ ");
            wcscat(formattedContent, line + 2);
            wcscat(formattedContent, L"\r\n");
        }
        else if (wcslen(line) == 0)
        {
            // 空行
            wcscat(formattedContent, L"\r\n");
        }
        else
        {
            // 普通文本行 - 处理内联格式
            WCHAR *processedLine = (WCHAR *)malloc((wcslen(line) + 100) * sizeof(WCHAR));
            if (processedLine)
            {
                wcscpy(processedLine, line);

                // 简单处理粗体标记 **text** -> 【text】
                WCHAR *boldStart = wcsstr(processedLine, L"**");
                while (boldStart != NULL)
                {
                    WCHAR *boldEnd = wcsstr(boldStart + 2, L"**");
                    if (boldEnd != NULL)
                    {
                        *boldStart = L'【';
                        *(boldStart + 1) = L' ';
                        *boldEnd = L' ';
                        *(boldEnd + 1) = L'】';
                        boldStart = wcsstr(boldEnd + 2, L"**");
                    }
                    else
                    {
                        break;
                    }
                }

                wcscat(formattedContent, processedLine);
                wcscat(formattedContent, L"\r\n");
                free(processedLine);
            }
            else
            {
                wcscat(formattedContent, line);
                wcscat(formattedContent, L"\r\n");
            }
        }

        line = wcstok(NULL, L"\n", &context);
    }

    // 添加赞赏码图片信息
    wcscat(formattedContent, L"\r\n\r\n");
    wcscat(formattedContent, L"═══════════════════════════════════════\r\n");
    wcscat(formattedContent, L"              💝 支持作者\r\n");
    wcscat(formattedContent, L"═══════════════════════════════════════\r\n");
    wcscat(formattedContent, L"\r\n");
    wcscat(formattedContent, L"如果这个项目对您有帮助，欢迎赞赏支持！\r\n");
    wcscat(formattedContent, L"\r\n");
    wcscat(formattedContent, L"📱 扫描下方二维码进行赞赏：\r\n");
    wcscat(formattedContent, L"\r\n");
    wcscat(formattedContent, L"[赞赏码图片显示区域]\r\n");
    wcscat(formattedContent, L"图片路径: resource\\zanshangma.png\r\n");
    wcscat(formattedContent, L"\r\n");
    wcscat(formattedContent, L"💡 您的支持是我们持续改进的动力！\r\n");
    wcscat(formattedContent, L"\r\n");
    wcscat(formattedContent, L"═══════════════════════════════════════\r\n");
    wcscat(formattedContent, L"    感谢您的使用与支持！\r\n");
    wcscat(formattedContent, L"═══════════════════════════════════════\r\n");

    // 添加文件头信息和格式化后的内容
    swprintf(previewContent, wideCharCount + 8192,
             L"📄 EveryView 项目文档 (README.md)\r\n"
             L"文件大小: %d 字节 | 编码: UTF-8\r\n\r\n%s",
             bytesRead, formattedContent);

    // 清理格式化内容的内存
    free(formattedContent);

    // 显示内容
    SetControlTextUTF8(hwndRight, previewContent);

    // 清理内存
    free(buffer);
    free(wideBuffer);
    free(previewContent);

    // 尝试显示赞赏码图片
    WCHAR imagePath[MAX_PATH];
    GetModuleFileNameW(NULL, imagePath, MAX_PATH);
    WCHAR *imageLastSlash = wcsrchr(imagePath, L'\\');
    if (imageLastSlash)
    {
        *(imageLastSlash + 1) = L'\0';
        wcscat(imagePath, L"resource\\zanshangma.bmp");
    }
    else
    {
        wcscpy(imagePath, L"resource\\zanshangma.bmp");
    }

    // 创建图片控件并显示赞赏码 - 只创建一次
    if (!g_imageControlCreated)
    {
        CreateSingleImageControl();
    }

    // 显示赞赏码图片
    if (IsImageControlValid())
    {
        DisplayImageInPreview(imagePath);
    }
}

// Preview selected file
void PreviewSelectedFile()
{
    if (!hwndLeft || !g_usnFiles)
        return;

    int selectedIndex = ListView_GetNextItem(hwndLeft, -1, LVNI_SELECTED);
    if (selectedIndex == -1)
    {
        SetControlTextUTF8(hwndRight, g_strings.previewPrompt);
        return;
    }

    // 使用与HandleVirtualListViewNotify相同的索引转换逻辑
    int actualIndex = selectedIndex;
    if (g_filterActive && g_filterBitmap && g_filteredCount > 0)
    {
        // 使用线性搜索找到第selectedIndex个匹配的文件
        int count = 0;
        actualIndex = -1;

        for (int i = 0; i < g_usnFileCount && count <= selectedIndex; i++)
        {
            if (GetFilterBit(i))
            {
                if (count == selectedIndex)
                {
                    actualIndex = i;
                    break;
                }
                count++;
            }
        }

        if (actualIndex == -1)
        {
            SetControlTextUTF8(hwndRight, g_strings.filterIndexError);
            return;
        }

        // 调试输出索引转换结果
        WCHAR debugMsg[256];
        swprintf(debugMsg, 256, L"预览索引转换: selectedIndex=%d -> actualIndex=%d, fileName=%s",
                 selectedIndex, actualIndex,
                 g_usnFiles[actualIndex].fileName ? g_usnFiles[actualIndex].fileName : L"NULL");
        OutputDebugStringW(debugMsg);
    }

    if (actualIndex >= 0 && actualIndex < g_usnFileCount)
    {
        // 重新构建完整的文件路径
        WCHAR fullFilePath[MAX_PATH * 2];

        if (g_usnFiles[actualIndex].isDirectory)
        {
            // 对于文件夹，使用专门的文件夹路径构建函数
            BuildFullFolderPath(actualIndex, fullFilePath);
        }
        else
        {
            // 对于文件，使用文件路径构建函数
            BuildFullFilePath(actualIndex, fullFilePath);
        }

        if (wcslen(fullFilePath) == 0)
        {
            wcscpy(fullFilePath, L"<Unknown File>");
        }

        LoadFilePreview(fullFilePath);
    }
}

// Create flat design font with DPI scaling
void CreateFlatFont()
{
    // 创建正常字体 (微软雅黑Light) - 使用当前字号
    hFont = CreateScaledFont(g_currentFontSize, L"Microsoft YaHei Light");
    if (!hFont)
    {
        hFont = CreateScaledFont(g_currentFontSize, L"Microsoft YaHei");
    }
    if (!hFont)
    {
        hFont = (HFONT)GetStockObject(DEFAULT_GUI_FONT);
    }

    // Create brushes for flat design
    hBackgroundBrush = CreateSolidBrush(FLAT_COLOR_BACKGROUND);
    hSplitterBrush = CreateSolidBrush(FLAT_COLOR_SPLITTER);
}

// Create bold font
void CreateBoldFont()
{
    // 创建加粗字体 (微软雅黑) - 使用当前字号
    hFontBold = CreateScaledBoldFont(g_currentFontSize, L"Microsoft YaHei");
    if (!hFontBold)
    {
        hFontBold = (HFONT)GetStockObject(DEFAULT_GUI_FONT);
    }
}

// Set font size and recreate fonts
void SetFontSize(int fontSize)
{
    g_currentFontSize = fontSize;
    g_config.fontSize = fontSize; // 更新配置
    RecreateAllFonts();
    UpdateInputHeight(); // 更新输入框高度和布局
    UpdateAllControlsFonts();
    UpdateListViewRowHeight(); // 更新ListView行高

    // 强制刷新所有UI控件（修复花屏问题）
    if (hwndMain)
    {
        // 1. 先重新布局所有控件
        UpdateLayout(hwndMain);

        // 2. 特殊处理ListView - 清除所有缓存
        if (hwndLeft && g_virtualItemCount > 0)
        {
            // 强制ListView重新计算所有项目
            SendMessage(hwndLeft, LVM_SETITEMCOUNT, g_virtualItemCount, LVSICF_NOINVALIDATEALL);
            if (g_virtualItemCount > 0)
            {
                SendMessage(hwndLeft, LVM_REDRAWITEMS, 0, g_virtualItemCount - 1);
            }

            // 清除ListView的内部缓存和字体缓存
            InvalidateRect(hwndLeft, NULL, TRUE);
            UpdateWindow(hwndLeft);

            // 强制ListView重新测量所有项目（修复花屏，保持滚动位置）
            int topIndex = ListView_GetTopIndex(hwndLeft);
            ListView_SetItemCount(hwndLeft, 0);
            ListView_SetItemCount(hwndLeft, g_virtualItemCount);

            // 恢复滚动位置
            if (topIndex > 0 && topIndex < g_virtualItemCount)
            {
                ListView_EnsureVisible(hwndLeft, topIndex, FALSE);
            }
        }

        // 3. 刷新其他控件
        if (hwndEdit)
        {
            InvalidateRect(hwndEdit, NULL, TRUE);
            UpdateWindow(hwndEdit);
        }
        if (hwndRight)
        {
            InvalidateRect(hwndRight, NULL, TRUE);
            UpdateWindow(hwndRight);
        }
        if (hwndStatus)
        {
            InvalidateRect(hwndStatus, NULL, TRUE);
            UpdateWindow(hwndStatus);
        }

        // 4. 最后刷新主窗口
        InvalidateRect(hwndMain, NULL, TRUE);
        UpdateWindow(hwndMain);

        // 5. 强制重新绘制整个窗口区域
        RedrawWindow(hwndMain, NULL, NULL, RDW_INVALIDATE | RDW_UPDATENOW | RDW_ALLCHILDREN);
    }

    SaveConfiguration(); // 保存配置
}

// Recreate all fonts with new size
void RecreateAllFonts()
{
    // 清理旧字体
    if (hFont)
    {
        DeleteObject(hFont);
        hFont = NULL;
    }
    if (hFontBold)
    {
        DeleteObject(hFontBold);
        hFontBold = NULL;
    }

    // 重新创建字体
    CreateFlatFont();
    CreateBoldFont();
}

// Set font style (normal or bold)
void SetFontStyle(BOOL useBold)
{
    g_useBoldFont = useBold;
    g_config.useBoldFont = useBold; // 更新配置
    UpdateAllControlsFonts();

    // 强制刷新UI以显示字体样式变化
    if (hwndMain)
    {
        // 强制重绘所有控件
        InvalidateRect(hwndMain, NULL, TRUE);
        if (hwndEdit)
            InvalidateRect(hwndEdit, NULL, TRUE);
        if (hwndLeft)
            InvalidateRect(hwndLeft, NULL, TRUE);
        if (hwndRight)
            InvalidateRect(hwndRight, NULL, TRUE);
        if (hwndStatus)
            InvalidateRect(hwndStatus, NULL, TRUE);

        // 立即更新显示
        UpdateWindow(hwndMain);
    }

    SaveConfiguration(); // 保存配置
}

// Update all controls with current font
void UpdateAllControlsFonts()
{
    HFONT currentFont = g_useBoldFont ? hFontBold : hFont;

    if (hwndEdit)
    {
        SendMessage(hwndEdit, WM_SETFONT, (WPARAM)currentFont, TRUE);

        // 重新设置输入框文字区域以适应新字号
        RECT rcEdit;
        GetClientRect(hwndEdit, &rcEdit);

        // 精确计算垂直居中的文字区域
        int editHeight = rcEdit.bottom - rcEdit.top;
        int textHeight = g_currentFontSize + 2; // 字体高度 + 2px缓冲
        int topMargin = (editHeight - textHeight) / 2;

        // 确保最小边距，避免文字贴边
        if (topMargin < 3)
            topMargin = 3;
        if (topMargin > editHeight / 3)
            topMargin = editHeight / 3; // 避免过大边距

        RECT textRect;
        textRect.left = 6; // 左边距6px，确保靠左显示
        textRect.top = topMargin;
        textRect.right = rcEdit.right - 6; // 右边距6px
        textRect.bottom = editHeight - topMargin;

        SendMessage(hwndEdit, EM_SETRECT, 0, (LPARAM)&textRect);
    }

    if (hwndLeft)
        SendMessage(hwndLeft, WM_SETFONT, (WPARAM)currentFont, TRUE);
    if (hwndRight)
        SendMessage(hwndRight, WM_SETFONT, (WPARAM)currentFont, TRUE);
    if (hwndStatus)
        SendMessage(hwndStatus, WM_SETFONT, (WPARAM)currentFont, TRUE);

    // 强制重绘所有控件
    if (hwndMain)
    {
        InvalidateRect(hwndMain, NULL, TRUE);
        UpdateWindow(hwndMain);
    }
}

// Draw flat splitter with DPI scaling
void DrawSplitter(HDC hdc, RECT *rect)
{
    RECT splitterRect;
    splitterRect.left = splitterPos;
    splitterRect.top = ScaleDPIY(INPUT_HEIGHT);
    splitterRect.right = splitterPos + ScaleDPIX(SPLITTER_WIDTH);
    splitterRect.bottom = rect->bottom - ScaleDPIY(STATUS_HEIGHT);

    FillRect(hdc, &splitterRect, hSplitterBrush);
}

// Create flat style controls with all features
void CreateControls(HWND hwnd)
{
    // Apply font settings from configuration before creating fonts
    g_currentFontSize = g_config.fontSize;
    g_useBoldFont = g_config.useBoldFont;

    // 初始化输入框高度
    g_currentInputHeight = CalculateLineHeight(g_currentFontSize) + 4;

    // Create flat design fonts with loaded settings
    CreateFlatFont();
    CreateBoldFont();

    // Create status bar
    hwndStatus = CreateWindow(
        STATUSCLASSNAME,
        NULL,
        WS_CHILD | WS_VISIBLE,
        0, 0, 0, 0,
        hwnd,
        NULL,
        GetModuleHandle(NULL),
        NULL);

    if (hwndStatus)
    {
        SendMessage(hwndStatus, WM_SETFONT, (WPARAM)hFont, TRUE);

        SetStatusBarText(g_strings.statusReady);
    }

    // Create filter input with DPI scaling - 左对齐，垂直居中
    hwndEdit = CreateWindowW(
        L"EDIT",
        L"",
        WS_CHILD | WS_VISIBLE | ES_LEFT | ES_AUTOHSCROLL,
        ScaleDPIX(BORDER_WIDTH), ScaleDPIY(BORDER_WIDTH),
        ScaleDPIX(400), ScaleDPIY(INPUT_HEIGHT),
        hwnd,
        (HMENU)1001,
        GetModuleHandle(NULL),
        NULL);

    if (hwndEdit)
    {
        SendMessage(hwndEdit, WM_SETFONT, (WPARAM)hFont, TRUE);
        SendMessageW(hwndEdit, EM_SETCUEBANNER, TRUE, (LPARAM)L"输入关键词筛选文件...");

        // 设置文字边距实现左对齐和垂直居中
        // 左边距4px实现靠左，上下边距实现垂直居中
        SendMessage(hwndEdit, EM_SETMARGINS, EC_LEFTMARGIN | EC_RIGHTMARGIN,
                    MAKELPARAM(4, 4));

        // 设置文字区域实现垂直居中
        RECT rcEdit;
        GetClientRect(hwndEdit, &rcEdit);

        // 精确计算垂直居中的文字区域
        int editHeight = rcEdit.bottom - rcEdit.top;
        int textHeight = g_currentFontSize + 2; // 字体高度 + 2px缓冲
        int topMargin = (editHeight - textHeight) / 2;

        // 确保最小边距，避免文字贴边
        if (topMargin < 3)
            topMargin = 3;
        if (topMargin > editHeight / 3)
            topMargin = editHeight / 3; // 避免过大边距

        RECT textRect;
        textRect.left = 6; // 左边距6px，确保靠左显示
        textRect.top = topMargin;
        textRect.right = rcEdit.right - 6; // 右边距6px
        textRect.bottom = editHeight - topMargin;

        SendMessage(hwndEdit, EM_SETRECT, 0, (LPARAM)&textRect);
    }

    // Create left ListView with virtual mode support
    hwndLeft = CreateWindowW(
        WC_LISTVIEWW,
        L"",
        WS_CHILD | WS_VISIBLE | LVS_REPORT | LVS_SINGLESEL | LVS_OWNERDATA,
        BORDER_WIDTH, INPUT_HEIGHT,
        splitterPos, 400,
        hwnd,
        (HMENU)1002,
        GetModuleHandle(NULL),
        NULL);

    if (hwndLeft)
    {
        // 初始化文件图标
        InitializeFileIcons();

        // 设置图标列表
        if (g_hImageList)
        {
            ListView_SetImageList(hwndLeft, g_hImageList, LVSIL_SMALL);
        }

        // 设置虚拟ListView扩展样式
        ListView_SetExtendedListViewStyle(hwndLeft,
                                          LVS_EX_FULLROWSELECT | LVS_EX_GRIDLINES | LVS_EX_DOUBLEBUFFER |
                                              LVS_EX_HEADERDRAGDROP);

        SendMessage(hwndLeft, WM_SETFONT, (WPARAM)hFont, TRUE);

        // Add columns using configuration settings
        LVCOLUMNW lvc = {0};
        lvc.mask = LVCF_TEXT | LVCF_WIDTH | LVCF_FMT;

        // Create columns in default order first
        for (int i = 0; i < 4; i++)
        {
            lvc.pszText = (WCHAR *)g_strings.columnNames[i];
            lvc.cx = g_config.columnWidths[i];

            // 设置列对齐方式：大小列（索引2）右对齐，其他列左对齐
            if (i == 2)
            {
                lvc.fmt = LVCFMT_RIGHT;
            }
            else
            {
                lvc.fmt = LVCFMT_LEFT;
            }

            SendMessageW(hwndLeft, LVM_INSERTCOLUMNW, i, (LPARAM)&lvc);
        }

        // Apply saved column order
        ListView_SetColumnOrderArray(hwndLeft, 4, g_config.columnOrder);

        // 设置ListView子类化以捕获滚动消息
        SetWindowSubclass(hwndLeft, ListViewSubclassProc, 1, 0);
    }

    // Create right panel for file preview
    hwndRight = CreateWindowW(
        L"EDIT",
        g_strings.previewPrompt,
        WS_CHILD | WS_VISIBLE | ES_MULTILINE | ES_READONLY | WS_VSCROLL | ES_AUTOVSCROLL,
        splitterPos + SPLITTER_WIDTH, INPUT_HEIGHT,
        400, 400,
        hwnd,
        (HMENU)1003,
        GetModuleHandle(NULL),
        NULL);

    if (hwndRight)
    {
        SendMessage(hwndRight, WM_SETFONT, (WPARAM)hFont, TRUE);

        // 设置右侧预览区域子类化以处理滚动和重绘事件
        SetWindowSubclass(hwndRight, PreviewSubclassProc, 2, 0);

        // 在创建预览框后立即加载README.md内容
        LoadReadmePreview();
    }

    // Apply the correct font style based on configuration
    UpdateAllControlsFonts();
}

// Update layout with flat design and DPI scaling (no gaps)
void UpdateLayout(HWND hwnd)
{
    RECT rect;
    GetClientRect(hwnd, &rect);

    // 使用DPI缩放的尺寸（输入框高度不缩放，直接使用像素值）
    int scaledBorderWidth = ScaleDPIX(BORDER_WIDTH);
    int scaledInputHeight = g_currentInputHeight; // 直接使用动态输入框高度（像素值）
    int scaledStatusHeight = ScaleDPIY(STATUS_HEIGHT);
    int scaledSplitterWidth = ScaleDPIX(SPLITTER_WIDTH);

    // Status bar at bottom
    if (hwndStatus)
    {
        SetWindowPos(hwndStatus, NULL,
                     0, rect.bottom - scaledStatusHeight,
                     rect.right, scaledStatusHeight,
                     SWP_NOZORDER);
    }

    // Filter input at top (no gap) - 左对齐，垂直居中
    if (hwndEdit)
    {
        SetWindowPos(hwndEdit, NULL,
                     scaledBorderWidth, scaledBorderWidth,
                     rect.right - (2 * scaledBorderWidth), scaledInputHeight,
                     SWP_NOZORDER);

        // 重新设置文字区域以保持垂直居中 - 使用动态字号
        RECT rcEdit;
        GetClientRect(hwndEdit, &rcEdit);

        // 精确计算垂直居中的文字区域
        int editHeight = rcEdit.bottom - rcEdit.top;
        int textHeight = g_currentFontSize + 2; // 字体高度 + 2px缓冲
        int topMargin = (editHeight - textHeight) / 2;

        // 确保最小边距，避免文字贴边
        if (topMargin < 3)
            topMargin = 3;
        if (topMargin > editHeight / 3)
            topMargin = editHeight / 3; // 避免过大边距

        RECT textRect;
        textRect.left = 6; // 左边距6px，确保靠左显示
        textRect.top = topMargin;
        textRect.right = rcEdit.right - 6; // 右边距6px
        textRect.bottom = editHeight - topMargin;

        SendMessage(hwndEdit, EM_SETRECT, 0, (LPARAM)&textRect);
    }

    // Left ListView (no gap)
    if (hwndLeft)
    {
        SetWindowPos(hwndLeft, NULL,
                     scaledBorderWidth, scaledInputHeight,
                     splitterPos - scaledBorderWidth,
                     rect.bottom - scaledInputHeight - scaledStatusHeight,
                     SWP_NOZORDER);
    }

    // Right panel (no gap)
    if (hwndRight)
    {
        SetWindowPos(hwndRight, NULL,
                     splitterPos + scaledSplitterWidth, scaledInputHeight,
                     rect.right - splitterPos - scaledSplitterWidth - scaledBorderWidth,
                     rect.bottom - scaledInputHeight - scaledStatusHeight,
                     SWP_NOZORDER);

        // 布局改变时，重新创建图片控件以确保正确显示
        if (IsImageControlValid())
        {
            UpdateImagePosition();
        }
    }
}

// Window procedure with all features
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    // 过滤频繁的无关消息，只记录重要消息
    if (uMsg != WM_SETCURSOR && uMsg != WM_NCMOUSEMOVE && uMsg != WM_NCHITTEST &&
        uMsg != WM_MOUSEMOVE && uMsg != WM_GETTEXT && uMsg != WM_GETTEXTLENGTH &&
        uMsg != WM_ERASEBKGND && uMsg != WM_NCPAINT && uMsg != 0x00a0 && uMsg != 0x0020 &&
        uMsg != 0x0084 && uMsg != 0x0200 && uMsg != 0x000d && uMsg != 0x000e)
    {
    }

    switch (uMsg)
    {
    case WM_CREATE:
        CreateControls(hwnd);

        // Apply splitter position from configuration
        if (g_config.splitterPosition > 0)
        {
            splitterPos = g_config.splitterPosition;
        }

        UpdateLayout(hwnd);

        // Apply maximized state if needed
        if (g_config.windowMaximized)
        {
            ShowWindow(hwnd, SW_MAXIMIZE);
        }

        g_mainWindowReady = TRUE;

        // 先清理可能存在的图片控件
        CleanupImageControl();

        // 延迟创建测试按钮，确保所有控件都已创建完成
        SetTimer(hwnd, 2000, 1000, NULL); // 1秒后创建测试按钮

        return 0;

    case WM_SIZE:
        UpdateLayout(hwnd);
        InvalidateRect(hwnd, NULL, TRUE); // Redraw splitter

        // Save configuration on resize (with delay to avoid frequent saves)
        SetTimer(hwnd, 1, 1000, NULL); // Save after 1 second
        return 0;

    case WM_TIMER:
        if (wParam == 1)
        {
            KillTimer(hwnd, 1);
            SaveConfiguration();
        }
        else if (wParam == 2000) // 延迟创建图片控件
        {
            KillTimer(hwnd, 2000);

            // 创建单个图片控件（只创建一次）
            if (hwndRight && IsWindow(hwndRight) && !g_imageControlCreated)
            {
                CreateSingleImageControl();
            }
        }
        else if (wParam == 1001) // 筛选防抖动计时器
        {
            KillTimer(hwnd, 1001);
            g_filterTimerId = 0;
            // 执行延迟的筛选操作
            ApplyFilter(g_pendingInputText);
        }
        else if (wParam == 1002) // ListView刷新计时器
        {
            KillTimer(hwnd, 1002);

            // 检查滚动是否已停止
            DWORD currentTime = GetTickCount();
            if (currentTime - g_lastScrollTime >= 150) // 150ms内无滚动活动
            {
                g_isScrolling = FALSE;
                // 只刷新可见区域，不强制重置ListView
                if (hwndLeft && IsWindow(hwndLeft))
                {
                    RECT clientRect;
                    GetClientRect(hwndLeft, &clientRect);
                    InvalidateRect(hwndLeft, &clientRect, FALSE);
                    UpdateWindow(hwndLeft);
                }
            }
            else
            {
                // 滚动仍在进行，重新设置定时器
                SetTimer(hwnd, 1002, 50, NULL);
            }
        }
        return 0;

    case WM_PAINT:
    {
        PAINTSTRUCT ps;
        HDC hdc = BeginPaint(hwnd, &ps);

        RECT rect;
        GetClientRect(hwnd, &rect);

        // Draw flat background
        FillRect(hdc, &rect, hBackgroundBrush);

        // Draw 1px splitter
        DrawSplitter(hdc, &rect);

        EndPaint(hwnd, &ps);
        return 0;
    }

    case WM_COMMAND:
        if (LOWORD(wParam) == 1001) // Filter edit box
        {
            if (HIWORD(wParam) == EN_CHANGE)
            {
                // Filter text changed - apply filter
                OnFilterTextChanged();
            }
        }
        return 0;

    case WM_NOTIFY:
    {
        LPNMHDR pnmh = (LPNMHDR)lParam;
        if (pnmh->hwndFrom == hwndLeft)
        {
            if (pnmh->code == LVN_GETDISPINFOW)
            {
                // 虚拟ListView数据请求
                NMLVDISPINFOW *pDispInfo = (NMLVDISPINFOW *)lParam;
                return HandleVirtualListViewNotify(pDispInfo);
            }
            else if (pnmh->code == LVN_ITEMCHANGED)
            {
                LPNMLISTVIEW pnmlv = (LPNMLISTVIEW)lParam;
                if (pnmlv->uNewState & LVIS_SELECTED)
                {
                    // File selection changed - update preview
                    PreviewSelectedFile();
                }
            }
            else if (pnmh->code == LVN_COLUMNCLICK)
            {
                // Column header clicked - implement sorting
                LPNMLISTVIEW pnmlv = (LPNMLISTVIEW)lParam;
                SortFileList(pnmlv->iSubItem);
            }
            else if (pnmh->code == NM_RCLICK)
            {
                // 右键点击处理
                LPNMITEMACTIVATE pnmia = (LPNMITEMACTIVATE)lParam;
                HandleListViewRightClick(pnmia);
            }
            else if (pnmh->code == NM_DBLCLK)
            {
                // 双击处理
                LPNMITEMACTIVATE pnmia = (LPNMITEMACTIVATE)lParam;
                HandleListViewDoubleClick(pnmia);
            }
            else if (pnmh->code == LVN_ODCACHEHINT)
            {
                // ListView缓存提示，用于优化虚拟模式性能
                // 当ListView滚动时会发送此消息
                NMLVCACHEHINT *pCacheHint = (NMLVCACHEHINT *)lParam;
                // 可以在这里预加载数据，但目前我们的数据已经在内存中
            }
        }
        else if (pnmh->hwndFrom == ListView_GetHeader(hwndLeft))
        {
            if (pnmh->code == HDN_ENDTRACK || pnmh->code == HDN_ENDTRACKW)
            {
                // Column width changed - save new widths
                SaveColumnSettings();
            }
            else if (pnmh->code == HDN_ENDDRAG)
            {
                // Column order changed - save new order
                SaveColumnSettings();
            }
        }
        return 0;
    }

    case WM_LBUTTONDOWN:
    {
        int x = LOWORD(lParam);
        int y = HIWORD(lParam);

        // Check if clicking on splitter (1px wide)
        if (x >= splitterPos && x <= splitterPos + SPLITTER_WIDTH && y >= INPUT_HEIGHT)
        {
            isDragging = TRUE;
            SetCapture(hwnd);
            SetCursor(LoadCursor(NULL, IDC_SIZEWE));
        }
        return 0;
    }

    case WM_LBUTTONUP:
        if (isDragging)
        {
            isDragging = FALSE;
            ReleaseCapture();
            SaveConfiguration(); // Save splitter position
        }
        return 0;

    case WM_MOUSEMOVE:
    {
        int x = LOWORD(lParam);
        int y = HIWORD(lParam);

        if (isDragging)
        {
            // Update splitter position
            RECT rect;
            GetClientRect(hwnd, &rect);

            if (x > 200 && x < rect.right - 200) // Min width constraints
            {
                splitterPos = x;
                UpdateLayout(hwnd);
                InvalidateRect(hwnd, NULL, TRUE);
            }
        }
        else if (x >= splitterPos && x <= splitterPos + SPLITTER_WIDTH && y >= INPUT_HEIGHT)
        {
            // Show resize cursor over splitter
            SetCursor(LoadCursor(NULL, IDC_SIZEWE));
        }
        else
        {
            SetCursor(LoadCursor(NULL, IDC_ARROW));
        }
        return 0;
    }

    case WM_USER + 1: // Refresh UI after USN scan

        UpdateStatusBar();
        return 0;

    case WM_USER + 2: // Refresh UI after file change

        UpdateStatusBar();
        return 0;

    case WM_USER + 5: // One-time render from delayed loading thread

        // 检查USN数据状态并渲染
        if (!g_dataLoaded && g_usnFileCount > 0 && g_usnFiles != NULL)
        {

            if (IsWindow(hwndLeft))
            {
                RenderUSNFileTree();
            }
        }
        return 0;

    case WM_USER + 6: // 筛选完成消息
        // 在这里才更新 g_virtualItemCount
        if (g_filterActive)
        {
            if (g_filteredCount > 0)
            {
                g_virtualItemCount = g_filteredCount;
            }
            else
            {
                // 如果没有匹配结果，设置为1以显示"无匹配结果"
                g_virtualItemCount = 1;
            }
        }
        else
        {
            g_virtualItemCount = g_usnFileCount;
        }

        // 强制刷新ListView显示筛选结果
        if (hwndLeft && IsWindow(hwndLeft))
        {
            // 调试输出
            WCHAR debugMsg[256];
            swprintf(debugMsg, 256, L"WM_USER+6: 开始更新ListView, virtualCount=%d, filterActive=%d, filteredCount=%d, usnFileCount=%d",
                     g_virtualItemCount, g_filterActive, g_filteredCount, g_usnFileCount);
            OutputDebugStringW(debugMsg);

            // 暂停重绘
            SendMessage(hwndLeft, WM_SETREDRAW, FALSE, 0);

            // 直接设置ListView项目数量，不要先清零
            ListView_SetItemCount(hwndLeft, g_virtualItemCount);

            // 验证ListView项目数量是否正确设置
            int actualCount = ListView_GetItemCount(hwndLeft);
            swprintf(debugMsg, 256, L"ListView项目数量验证: 设置=%d, 实际=%d", g_virtualItemCount, actualCount);
            OutputDebugStringW(debugMsg);

            // 检查ListView样式
            DWORD style = GetWindowLong(hwndLeft, GWL_STYLE);
            BOOL hasOwnerData = (style & LVS_OWNERDATA) != 0;
            swprintf(debugMsg, 256, L"ListView样式检查: style=0x%x, hasOwnerData=%d", style, hasOwnerData);
            OutputDebugStringW(debugMsg);

            // 恢复重绘
            SendMessage(hwndLeft, WM_SETREDRAW, TRUE, 0);

            // 强制完全重绘
            InvalidateRect(hwndLeft, NULL, TRUE);
            UpdateWindow(hwndLeft);

            // 强制ListView重新请求数据
            if (g_virtualItemCount > 0)
            {
                ListView_RedrawItems(hwndLeft, 0, min(g_virtualItemCount - 1, 100));
            }

            swprintf(debugMsg, 256, L"WM_USER+6: ListView更新完成");
            OutputDebugStringW(debugMsg);

            // 筛选完成后重新调整列宽以适应新的数据
            AutoAdjustAllColumnWidths();
        }

        // 更新状态栏
        UpdateStatusBar();
        return 0;

    case WM_USER + 9: // 筛选开始 - 保持当前ListView状态
    {
        WCHAR debugMsg[256];
        swprintf(debugMsg, 256, L"WM_USER+9: 筛选开始，保持当前ListView状态");
        OutputDebugStringW(debugMsg);

        // 不做任何ListView更新，保持当前显示状态
        // 这样用户在筛选过程中仍能看到原有内容
    }
        return 0;

    case WM_USER + 7: // 筛选进度更新（减少UI卡滞）
        // 轻量级进度更新，只更新状态栏，不重绘ListView
        {
            int processedCount = (int)wParam;
            int matchedCount = (int)lParam;

            // 只更新状态栏，避免频繁重绘ListView
            if (hwndStatus && IsWindow(hwndStatus))
            {
                WCHAR progressText[256];
                if (g_currentLanguage == APP_LANG_CHINESE)
                {
                    swprintf(progressText, 256, L"筛选中... 已处理 %d 个文件，找到 %d 个匹配",
                             processedCount, matchedCount);
                }
                else
                {
                    swprintf(progressText, 256, L"Filtering... Processed %d files, found %d matches",
                             processedCount, matchedCount);
                }
                SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)progressText);
            }
        }
        return 0;

    case WM_USER + 8: // 排序完成
        // 刷新ListView显示
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_usnFileCount);
            InvalidateRect(hwndLeft, NULL, TRUE);
        }
        UpdateStatusBar();
        return 0;

    case WM_DPICHANGED: // DPI变化处理
        HandleDPIChanged(hwnd, wParam, lParam);
        return 0;

    case WM_USER + 10: // 重新布局（DPI变化后）
        UpdateLayout(hwnd);

        // 重新创建字体以适应新的DPI
        RecreateAllFonts();
        UpdateAllControlsFonts();
        return 0;

    case WM_USER + 100: // 延迟创建图片控件
        // 现在主窗口应该已经完全初始化了，但图片控件由定时器2000负责创建
        // 这里只需要刷新界面
        InvalidateRect(hwnd, NULL, TRUE);
        return 0;

    case WM_DESTROY:
        // Save configuration before exit
        SaveConfiguration();

        // 首先清理筛选线程（防止访问已释放的内存）
        CleanupFilterThread();

        // 清理文件图标资源
        CleanupFileIcons();

        // Stop background threads
        if (g_usnReadThread)
        {
            WaitForSingleObject(g_usnReadThread, 5000);
            CloseHandle(g_usnReadThread);
            g_usnReadThread = NULL;
        }
        StopFileWatcher();

        // Clean up USN memory
        CleanupUSNMemory();
        DeleteCriticalSection(&g_usnDataCriticalSection);

        // Clean up resources
        if (hFont)
            DeleteObject(hFont);
        if (hFontBold)
            DeleteObject(hFontBold);

        // Clean up image control
        CleanupImageControl();

        // Clean up image resources
        if (hBitmap)
        {
            DeleteObject(hBitmap);
            hBitmap = NULL;
        }
        if (hwndImage)
        {
            DestroyWindow(hwndImage);
            hwndImage = NULL;
            g_imageControlCreated = FALSE; // 重置状态
        }
        if (hBackgroundBrush)
            DeleteObject(hBackgroundBrush);
        if (hSplitterBrush)
            DeleteObject(hSplitterBrush);

        // 清理筛选相关内存
        CleanupFilterBitmap();

        // 清理临界区
        DeleteCriticalSection(&g_dataCriticalSection);

        PostQuitMessage(0);
        return 0;

    default:
        return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
}

// Main function
int main(int argc, char *argv[])
{
    // 第一步：初始化DPI感知（必须在创建任何窗口之前）
    InitializeDPIAwareness();

    // 初始化临界区
    InitializeCriticalSection(&g_dataCriticalSection);

    // Set UTF-8 support for Chinese characters
    setlocale(LC_ALL, "");

    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_LISTVIEW_CLASSES | ICC_BAR_CLASSES;

    if (!InitCommonControlsEx(&icex))
    {
        MessageBoxW(NULL, L"Failed to initialize common controls", L"Error", MB_OK);
        return 1;
    }

    // Get instance handle
    HINSTANCE hInstance = GetModuleHandle(NULL);

    // Register window class
    const WCHAR *szWindowClass = L"CompleteFilePreviewerClass";
    WNDCLASSW wc = {0};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hbrBackground = NULL; // We'll handle painting ourselves
    wc.lpszClassName = szWindowClass;
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    // 使用嵌入的资源图标
    wc.hIcon = LoadIcon(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON));

    if (!RegisterClassW(&wc))
    {
        MessageBoxW(NULL, L"Window Registration Failed!", L"Error", MB_OK);
        return 1;
    }

    // 初始化USN内存
    InitializeUSNMemory();

    // 初始化筛选线程
    InitializeFilterThread();

    // 初始化筛选位图
    InitializeFilterBitmap();

    // 加载配置和扫描状态
    LoadConfiguration(); // config.ini (includes LoadScanStatus)

    // 初始化语言字符串
    InitializeLanguageStrings();

    // Determine window position and size from configuration
    int windowX = (g_config.windowX > 0) ? g_config.windowX : CW_USEDEFAULT;
    int windowY = (g_config.windowY > 0) ? g_config.windowY : CW_USEDEFAULT;
    int windowWidth = (g_config.windowWidth > 0) ? g_config.windowWidth : DEFAULT_WINDOW_WIDTH;
    int windowHeight = (g_config.windowHeight > 0) ? g_config.windowHeight : DEFAULT_WINDOW_HEIGHT;

    // Create main window
    hwndMain = CreateWindowW(
        szWindowClass,
        g_strings.windowTitle,
        WS_OVERLAPPEDWINDOW | WS_CLIPCHILDREN,
        windowX, windowY,
        windowWidth, windowHeight,
        NULL, NULL, hInstance, NULL);

    if (!hwndMain)
    {
        DWORD error = GetLastError();
        MessageBoxW(NULL, L"Window Creation Failed!", L"Error", MB_OK);
        return 1;
    }

    // 初始化DPI设置
    UpdateDPISettings(hwndMain);

    // 为窗口设置图标 - 优先使用高质量PNG文件，支持高DPI
    HICON hBigIcon = NULL;
    HICON hSmallIcon = NULL;

    // 获取DPI缩放比例
    HDC hdc = GetDC(NULL);
    int dpiX = GetDeviceCaps(hdc, LOGPIXELSX);
    ReleaseDC(NULL, hdc);
    float dpiScale = dpiX / 96.0f; // 96 DPI是标准DPI

    // 方法1：尝试从PNG文件加载最高质量图标
    if (GetFileAttributesW(L"resource\\icons8_512.png") != INVALID_FILE_ATTRIBUTES)
    {
        // 获取系统推荐的图标尺寸并考虑DPI缩放
        int bigIconSize = GetSystemMetrics(SM_CXICON);     // 通常是32或48
        int smallIconSize = GetSystemMetrics(SM_CXSMICON); // 通常是16

        // 根据DPI缩放调整图标尺寸
        bigIconSize = (int)(bigIconSize * dpiScale);
        smallIconSize = (int)(smallIconSize * dpiScale);

        // 为桌面显示优化图标尺寸
        // 桌面图标通常需要更大的尺寸才能清晰显示
        if (bigIconSize < 64)
            bigIconSize = 64; // 桌面最小64x64
        if (dpiScale > 1.25f && bigIconSize < 96)
            bigIconSize = 96; // 高DPI时使用96x96
        if (bigIconSize > 256)
            bigIconSize = 256; // 最大256x256
        if (smallIconSize < 16)
            smallIconSize = 16; // 最小16x16用于标题栏

        // 加载大图标 (用于桌面显示)
        hBigIcon = (HICON)LoadImageW(NULL, L"resource\\icons8_512.png",
                                     IMAGE_ICON, bigIconSize, bigIconSize, LR_LOADFROMFILE);
        // 加载小图标 (用于标题栏)
        hSmallIcon = (HICON)LoadImageW(NULL, L"resource\\icons8_512.png",
                                       IMAGE_ICON, smallIconSize, smallIconSize, LR_LOADFROMFILE);
    }

    // 方法2：如果PNG不可用，尝试从ICO文件加载
    if (hBigIcon == NULL)
    {
        int bigIconSize = GetSystemMetrics(SM_CXICON);
        if (bigIconSize < 48)
            bigIconSize = 48;

        if (GetFileAttributesW(L"resource\\icons8_256.ico") != INVALID_FILE_ATTRIBUTES)
        {
            hBigIcon = (HICON)LoadImageW(NULL, L"resource\\icons8_256.ico",
                                         IMAGE_ICON, bigIconSize, bigIconSize, LR_LOADFROMFILE);
        }
    }

    if (hSmallIcon == NULL)
    {
        int smallIconSize = GetSystemMetrics(SM_CXSMICON);

        if (GetFileAttributesW(L"resource\\icons8_16.ico") != INVALID_FILE_ATTRIBUTES)
        {
            hSmallIcon = (HICON)LoadImageW(NULL, L"resource\\icons8_16.ico",
                                           IMAGE_ICON, smallIconSize, smallIconSize, LR_LOADFROMFILE);
        }
    }

    // 方法3：如果外部文件都不可用，使用嵌入资源
    if (hBigIcon == NULL)
    {
        // 尝试多种尺寸的嵌入图标
        hBigIcon = (HICON)LoadImage(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON),
                                    IMAGE_ICON, 48, 48, LR_DEFAULTCOLOR);
        if (hBigIcon == NULL)
        {
            hBigIcon = (HICON)LoadImage(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON),
                                        IMAGE_ICON, 0, 0, LR_DEFAULTSIZE);
        }
    }

    if (hSmallIcon == NULL)
    {
        hSmallIcon = (HICON)LoadImage(hInstance, MAKEINTRESOURCE(IDI_MAIN_ICON),
                                      IMAGE_ICON, 16, 16, LR_DEFAULTCOLOR);
    }

    // 设置图标 - 为桌面显示优化
    if (hBigIcon != NULL)
    {
        SendMessage(hwndMain, WM_SETICON, ICON_BIG, (LPARAM)hBigIcon);

        // 为桌面显示额外加载更大尺寸的图标
        HICON hDesktopIcon = NULL;
        if (GetFileAttributesW(L"resource\\icons8_512.png") != INVALID_FILE_ATTRIBUTES)
        {
            // 桌面专用大图标 - 根据DPI选择最佳尺寸
            int desktopSize = 64; // 默认64x64
            if (dpiScale > 1.25f)
                desktopSize = 96; // 125%缩放时用96x96
            if (dpiScale > 1.5f)
                desktopSize = 128; // 150%缩放时用128x128
            if (dpiScale > 2.0f)
                desktopSize = 256; // 200%缩放时用256x256

            hDesktopIcon = (HICON)LoadImageW(NULL, L"resource\\icons8_512.png",
                                             IMAGE_ICON, desktopSize, desktopSize,
                                             LR_LOADFROMFILE | LR_SHARED);
            if (hDesktopIcon)
            {
                // 替换为桌面优化的大图标
                SendMessage(hwndMain, WM_SETICON, ICON_BIG, (LPARAM)hDesktopIcon);
            }
        }
    }

    if (hSmallIcon != NULL)
    {
        SendMessage(hwndMain, WM_SETICON, ICON_SMALL, (LPARAM)hSmallIcon);
    }

    SetWindowTextW(hwndMain, L"EveryView");

    // Show window
    ShowWindow(hwndMain, SW_SHOW);
    UpdateWindow(hwndMain);

    // Ensure window is brought to foreground
    SetForegroundWindow(hwndMain);
    BringWindowToTop(hwndMain);
    SetFocus(hwndMain);

    StartUSNReadThread();

    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0))
    {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // 清理工作已在WM_DESTROY中完成
    return (int)msg.wParam;
}

// 优化的排序比较函数 - 减少分支和函数调用开销
int CompareUSNFiles(const void *a, const void *b)
{
    const USNFileData *fileA = (const USNFileData *)a;
    const USNFileData *fileB = (const USNFileData *)b;
    int result = 0;

    switch (g_sortColumn)
    {
    case 0: // 名称列 - 使用内联比较避免函数调用
    {
        const WCHAR *strA = fileA->fileName ? fileA->fileName : L"";
        const WCHAR *strB = fileB->fileName ? fileB->fileName : L"";
        // 快速内联字符串比较
        while (*strA && *strB)
        {
            WCHAR cA = (*strA >= L'A' && *strA <= L'Z') ? *strA + 32 : *strA;
            WCHAR cB = (*strB >= L'A' && *strB <= L'Z') ? *strB + 32 : *strB;
            if (cA != cB)
            {
                result = cA - cB;
                break;
            }
            strA++;
            strB++;
        }
        if (result == 0)
            result = *strA - *strB;
    }
    break;
    case 1: // 路径列
    {
        const WCHAR *pathA = fileA->fullPath ? fileA->fullPath : L"";
        const WCHAR *pathB = fileB->fullPath ? fileB->fullPath : L"";
        result = _wcsicmp(pathA, pathB);
    }
    break;
    case 2: // 大小列 - 优化分支逻辑
    {
        int dirA = fileA->isDirectory;
        int dirB = fileB->isDirectory;
        if (dirA != dirB)
        {
            result = dirB - dirA; // 文件夹排在前面
        }
        else if (dirA) // 两个都是文件夹
        {
            const WCHAR *nameA = fileA->fileName ? fileA->fileName : L"";
            const WCHAR *nameB = fileB->fileName ? fileB->fileName : L"";
            result = _wcsicmp(nameA, nameB);
        }
        else // 两个都是文件，使用三元比较避免分支
        {
            result = (fileA->fileSize > fileB->fileSize) - (fileA->fileSize < fileB->fileSize);
        }
    }
    break;
    case 3: // 修改时间列 - 使用三元比较避免分支
        result = (fileA->lastWriteTime > fileB->lastWriteTime) - (fileA->lastWriteTime < fileB->lastWriteTime);
        break;
    default:
        result = 0;
        break;
    }

    return g_sortAscending ? result : -result;
}

// 反转文件列表（用于快速切换排序方向）
void ReverseFileList()
{
    if (g_usnFileCount <= 1)
        return;

    USNFileData temp;
    int start = 0;
    int end = g_usnFileCount - 1;

    while (start < end)
    {
        // 交换元素
        temp = g_usnFiles[start];
        g_usnFiles[start] = g_usnFiles[end];
        g_usnFiles[end] = temp;

        start++;
        end--;
    }
}

// 排序线程参数结构
typedef struct
{
    int column;
    BOOL ascending;
} SortThreadParam;

// 优化的排序线程函数 - 支持进度更新和中断
DWORD WINAPI SortThreadProc(LPVOID lpParam)
{
    SortThreadParam *param = (SortThreadParam *)lpParam;

    // 进入临界区进行排序
    EnterCriticalSection(&g_usnDataCriticalSection);

    g_sortColumn = param->column;
    g_sortAscending = param->ascending;

    // 对于超大数据集，考虑使用分块排序以提供进度反馈
    if (g_usnFileCount > 500000)
    {
        // 显示排序进度状态
        if (hwndStatus && IsWindow(hwndStatus))
        {
            SetStatusBarText(g_strings.statusSorting);
        }
    }

    // 使用优化的qsort进行排序
    qsort(g_usnFiles, g_usnFileCount, sizeof(USNFileData), CompareUSNFiles);

    LeaveCriticalSection(&g_usnDataCriticalSection);

    // 通知主线程排序完成
    if (hwndMain && IsWindow(hwndMain))
    {
        PostMessage(hwndMain, WM_USER + 7, 0, 0); // 自定义消息：排序完成
    }

    g_sortInProgress = FALSE;
    free(param);

    return 0;
}

// 优化的筛选结果排序 - 使用索引数组避免大量内存拷贝
void SortFilteredResults(int column)
{
    if (!g_filterActive || !g_filterBitmap || g_filteredCount == 0)
    {
        return;
    }

    // 创建索引数组而非数据拷贝，大幅减少内存使用
    int *filteredIndices = malloc(g_filteredCount * sizeof(int));
    if (!filteredIndices)
    {
        return;
    }

    // 收集筛选项的索引
    int filteredIndex = 0;
    for (int i = 0; i < g_usnFileCount && filteredIndex < g_filteredCount; i++)
    {
        if (GetFilterBit(i))
        {
            filteredIndices[filteredIndex] = i;
            filteredIndex++;
        }
    }

    // 设置排序参数
    g_sortColumn = column;

    // 使用索引排序，比较实际数据但只交换索引
    for (int i = 0; i < g_filteredCount - 1; i++)
    {
        for (int j = i + 1; j < g_filteredCount; j++)
        {
            if (CompareUSNFiles(&g_usnFiles[filteredIndices[i]], &g_usnFiles[filteredIndices[j]]) > 0)
            {
                int temp = filteredIndices[i];
                filteredIndices[i] = filteredIndices[j];
                filteredIndices[j] = temp;
            }
        }
    }

    // 根据排序后的索引重新排列数据
    USNFileData *tempData = malloc(g_filteredCount * sizeof(USNFileData));
    if (tempData)
    {
        for (int i = 0; i < g_filteredCount; i++)
        {
            tempData[i] = g_usnFiles[filteredIndices[i]];
        }

        // 写回原位置
        filteredIndex = 0;
        for (int i = 0; i < g_usnFileCount && filteredIndex < g_filteredCount; i++)
        {
            if (GetFilterBit(i))
            {
                g_usnFiles[i] = tempData[filteredIndex];
                filteredIndex++;
            }
        }
        free(tempData);
    }

    free(filteredIndices);
}

// 排序文件列表（优化版本，支持筛选）
void SortFileList(int column)
{
    if (g_usnFileCount == 0)
        return;

    if (g_sortInProgress)
    {
        return;
    }

    BOOL newAscending = TRUE;

    if (g_sortColumn == column)
    {
        newAscending = !g_sortAscending;
    }
    else
    {

        newAscending = TRUE;
    }

    g_sortAscending = newAscending;

    // 检查是否有活动筛选
    if (g_filterActive && g_filterBitmap && g_filteredCount > 0)
    {

        EnterCriticalSection(&g_dataCriticalSection);
        SortFilteredResults(column);
        LeaveCriticalSection(&g_dataCriticalSection);

        // 刷新ListView显示
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_virtualItemCount);
            InvalidateRect(hwndLeft, NULL, TRUE);
        }

        return;
    }

    // 优化：降低后台线程阈值，提高响应性
    if (g_usnFileCount >= 50000)
    {
        g_sortInProgress = TRUE;

        SortThreadParam *param = malloc(sizeof(SortThreadParam));
        param->column = column;
        param->ascending = newAscending;

        g_sortThread = CreateThread(NULL, 0, SortThreadProc, param, 0, NULL);
        if (g_sortThread == NULL)
        {
            g_sortInProgress = FALSE;
            free(param);
        }
    }
    else
    {
        EnterCriticalSection(&g_usnDataCriticalSection);

        g_sortColumn = column;
        g_sortAscending = newAscending;

        // 对于小数据集使用优化的快速排序
        qsort(g_usnFiles, g_usnFileCount, sizeof(USNFileData), CompareUSNFiles);

        LeaveCriticalSection(&g_usnDataCriticalSection);

        // 刷新ListView显示
        if (hwndLeft && IsWindow(hwndLeft))
        {
            ListView_SetItemCount(hwndLeft, g_usnFileCount);
            InvalidateRect(hwndLeft, NULL, TRUE);
        }
    }
}

// 转换文件时间为字符串
void ConvertFileTimeToString(const FILETIME *fileTime, char *timeString, size_t bufferSize)
{
    SYSTEMTIME st;
    if (FileTimeToSystemTime(fileTime, &st))
    {
        snprintf(timeString, bufferSize, "%04d/%02d/%02d-%02d:%02d",
                 st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute);
    }
    else
    {
        strncpy(timeString, "Unknown", bufferSize - 1);
        timeString[bufferSize - 1] = '\0';
    }
}

// 初始化图标缓存
void InitializeIconCache()
{
    InitializeCriticalSection(&g_iconCacheCriticalSection);
    g_iconCacheCount = 0;
    memset(g_iconCache, 0, sizeof(g_iconCache));

    // 预加载常见文件类型的图标
    const WCHAR *commonExtensions[] = {
        L".txt", L".doc", L".docx", L".pdf", L".xls", L".xlsx",
        L".ppt", L".pptx", L".jpg", L".jpeg", L".png", L".gif",
        L".bmp", L".mp3", L".mp4", L".avi", L".mkv", L".zip",
        L".rar", L".7z", L".exe", L".dll", L".sys", L".ini",
        L".log", L".xml", L".html", L".css", L".js", L".cpp",
        L".h", L".c", L".py", L".java", L".cs", L".php"};

    int numExtensions = sizeof(commonExtensions) / sizeof(commonExtensions[0]);

    for (int i = 0; i < numExtensions && g_iconCacheCount < MAX_ICON_CACHE_SIZE; i++)
    {
        // 创建一个临时文件名来获取图标
        WCHAR tempFileName[64];
        swprintf(tempFileName, 64, L"temp%s", commonExtensions[i]);

        int iconIndex = GetFileIconIndex(tempFileName, FALSE);

        // 添加到缓存
        wcscpy(g_iconCache[g_iconCacheCount].extension, commonExtensions[i]);
        g_iconCache[g_iconCacheCount].iconIndex = iconIndex;
        g_iconCache[g_iconCacheCount].isDirectory = FALSE;
        g_iconCacheCount++;
    }
}

// 清理图标缓存
void CleanupIconCache()
{
    DeleteCriticalSection(&g_iconCacheCriticalSection);
    g_iconCacheCount = 0;
}

// 初始化文件图标系统
void InitializeFileIcons()
{
    // 初始化图标缓存
    InitializeIconCache();

    // 获取系统图像列表
    g_hImageList = (HIMAGELIST)SHGetFileInfoW(L"C:\\",
                                              0,
                                              &g_shFileInfo,
                                              sizeof(g_shFileInfo),
                                              SHGFI_SYSICONINDEX | SHGFI_SMALLICON);
}

// 清理文件图标资源
void CleanupFileIcons()
{
    // 清理图标缓存
    CleanupIconCache();

    // 系统图像列表不需要手动销毁
    g_hImageList = NULL;
}

// 获取缓存的文件图标索引（优化版本）
int GetCachedFileIconIndex(const WCHAR *fileName, BOOL isDirectory)
{
    if (!fileName)
    {
        return isDirectory ? 3 : 0;
    }

    // 滚动时返回默认图标以提高性能
    if (g_isScrolling)
    {
        return isDirectory ? 3 : 0; // 滚动时使用默认图标
    }

    // 对于目录，直接返回文件夹图标
    if (isDirectory)
    {
        return 3; // 文件夹图标索引
    }

    // 提取文件扩展名
    WCHAR extension[32] = L"";
    const WCHAR *dot = wcsrchr(fileName, L'.');
    if (dot && wcslen(dot) < 32)
    {
        wcscpy(extension, dot);
        // 转换为小写以便比较
        for (int i = 0; extension[i]; i++)
        {
            if (extension[i] >= L'A' && extension[i] <= L'Z')
            {
                extension[i] += 32;
            }
        }
    }

    // 在缓存中查找
    EnterCriticalSection(&g_iconCacheCriticalSection);

    for (int i = 0; i < g_iconCacheCount; i++)
    {
        if (g_iconCache[i].isDirectory == isDirectory &&
            wcscmp(g_iconCache[i].extension, extension) == 0)
        {
            int iconIndex = g_iconCache[i].iconIndex;
            LeaveCriticalSection(&g_iconCacheCriticalSection);
            return iconIndex;
        }
    }

    LeaveCriticalSection(&g_iconCacheCriticalSection);

    // 缓存中没有找到，调用原始函数获取图标
    int iconIndex = GetFileIconIndex(fileName, isDirectory);

    // 将结果添加到缓存（如果缓存未满）
    EnterCriticalSection(&g_iconCacheCriticalSection);

    if (g_iconCacheCount < MAX_ICON_CACHE_SIZE)
    {
        wcscpy(g_iconCache[g_iconCacheCount].extension, extension);
        g_iconCache[g_iconCacheCount].iconIndex = iconIndex;
        g_iconCache[g_iconCacheCount].isDirectory = isDirectory;
        g_iconCacheCount++;
    }

    LeaveCriticalSection(&g_iconCacheCriticalSection);

    return iconIndex;
}

// 获取文件图标索引（原始版本，仅供缓存函数内部使用）
int GetFileIconIndex(const WCHAR *fileName, BOOL isDirectory)
{
    SHFILEINFOW shfi = {0};
    DWORD dwAttributes = isDirectory ? FILE_ATTRIBUTE_DIRECTORY : FILE_ATTRIBUTE_NORMAL;

    // 使用文件扩展名获取图标索引
    if (SHGetFileInfoW(fileName,
                       dwAttributes,
                       &shfi,
                       sizeof(shfi),
                       SHGFI_SYSICONINDEX | SHGFI_SMALLICON | SHGFI_USEFILEATTRIBUTES))
    {
        return shfi.iIcon;
    }

    // 如果失败，返回默认图标索引
    return isDirectory ? 3 : 0; // 3通常是文件夹图标，0是默认文件图标
}

// 初始化语言字符串
void InitializeLanguageStrings()
{
    // 根据当前语言设置字符串
    if (g_currentLanguage == APP_LANG_CHINESE)
    {
        // 中文字符串
        g_strings.columnNames[0] = L"名称";
        g_strings.columnNames[1] = L"路径";
        g_strings.columnNames[2] = L"大小";
        g_strings.columnNames[3] = L"修改时间";
        g_strings.menuOpenFolder = L"打开文件夹";
        g_strings.menuOpenFile = L"打开文件";
        g_strings.menuCopyPath = L"复制路径";
        g_strings.menuProperties = L"属性";
        g_strings.menuLanguage = L"语言";
        g_strings.menuChinese = L"中文";
        g_strings.menuEnglish = L"English";
        g_strings.statusReady = L"就绪";
        g_strings.statusScanning = L"正在扫描文件...";
        g_strings.statusFiltering = L"正在筛选...";
        g_strings.statusSorting = L"正在排序大量文件，请稍候...";
        g_strings.statusFiles = L"个文件";
        g_strings.statusFolders = L"个文件夹";
        g_strings.previewPrompt = L"请选择一个文件进行预览。";
        g_strings.folderSizeText = L"文件夹";
        g_strings.windowTitle = L"EveryView - 文件预览器";
        g_strings.fileNotFound = L"文件不存在或无法访问。";
        g_strings.fileInfoError = L"无法获取文件信息。";
        g_strings.filterIndexError = L"筛选索引错误。";
        g_strings.binaryFileInfo = L"这是二进制文件或不支持预览的文件类型。\r\n仅显示基本文件信息。";
        g_strings.previewTemplate = L"文件预览\r\n"
                                    L"========================================\r\n"
                                    L"文件名：%s\r\n"
                                    L"路径：%s\r\n"
                                    L"大小：%s\r\n"
                                    L"修改时间：%s\r\n"
                                    L"========================================\r\n\r\n";
        g_strings.fileContent = L"文件内容：\r\n----------------------------------------\r\n";
        g_strings.fileTooLarge = L"[文件过大，无法预览内容]";
        g_strings.cannotReadFile = L"[无法读取文件内容]";
        g_strings.imageFileInfo = L"这是图片文件。\r\n支持格式：JPG、PNG、GIF、BMP 等。\r\n[图片预览功能待实现]";
        g_strings.loadComplete = L"文件预览器 - 共 %d 个文件 (加载完成)";
        g_strings.scanProgress = L"正在扫描文件... 已找到 %d 个文件";
        g_strings.scanComplete = L"扫描完成 - 共找到 %d 个文件，等待筛选输入...";
        g_strings.usnReadFailed = L"USN读取失败";
    }
    else
    {
        // 英文字符串
        g_strings.columnNames[0] = L"Name";
        g_strings.columnNames[1] = L"Path";
        g_strings.columnNames[2] = L"Size";
        g_strings.columnNames[3] = L"Modified";
        g_strings.menuOpenFolder = L"Open Folder";
        g_strings.menuOpenFile = L"Open File";
        g_strings.menuCopyPath = L"Copy Path";
        g_strings.menuProperties = L"Properties";
        g_strings.menuLanguage = L"Language";
        g_strings.menuChinese = L"中文";
        g_strings.menuEnglish = L"English";
        g_strings.statusReady = L"Ready";
        g_strings.statusScanning = L"Scanning files...";
        g_strings.statusFiltering = L"Filtering...";
        g_strings.statusSorting = L"Sorting large dataset, please wait...";
        g_strings.statusFiles = L" files";
        g_strings.statusFolders = L" folders";
        g_strings.previewPrompt = L"Please select a file to preview.";
        g_strings.folderSizeText = L"Folder";
        g_strings.windowTitle = L"EveryView - File Previewer";
        g_strings.fileNotFound = L"File not found or inaccessible.";
        g_strings.fileInfoError = L"Unable to get file information.";
        g_strings.filterIndexError = L"Filter index error.";
        g_strings.binaryFileInfo = L"This is a binary file or unsupported file type.\r\nShowing basic file information only.";
        g_strings.previewTemplate = L"File Preview\r\n"
                                    L"========================================\r\n"
                                    L"Name: %s\r\n"
                                    L"Path: %s\r\n"
                                    L"Size: %s\r\n"
                                    L"Modified: %s\r\n"
                                    L"========================================\r\n\r\n";
        g_strings.fileContent = L"File Content:\r\n----------------------------------------\r\n";
        g_strings.fileTooLarge = L"[File too large to preview]";
        g_strings.cannotReadFile = L"[Cannot read file content]";
        g_strings.imageFileInfo = L"This is an image file.\r\nSupported formats: JPG, PNG, GIF, BMP, etc.\r\n[Image preview feature to be implemented]";
        g_strings.loadComplete = L"File Previewer - %d files total (Load complete)";
        g_strings.scanProgress = L"Scanning files... Found %d files";
        g_strings.scanComplete = L"Scan complete - Found %d files, waiting for filter input...";
        g_strings.usnReadFailed = L"USN read failed";
    }
}

// 设置语言
void SetLanguage(Language lang)
{
    if (g_currentLanguage != lang)
    {
        g_currentLanguage = lang;
        g_config.currentLanguage = lang;

        // 重新初始化语言字符串
        InitializeLanguageStrings();

        // 更新UI
        UpdateUILanguage();

        // 保存配置
        SaveConfiguration();
    }
}

// 更新UI语言
void UpdateUILanguage()
{
    // 更新窗口标题
    UpdateWindowTitle();

    // 更新列标题
    UpdateColumnHeaders();

    // 更新状态栏
    UpdateStatusBar();

    // 更新预览框提示文本
    if (hwndRight && IsWindow(hwndRight))
    {
        // 检查当前是否有选中的文件
        int selectedIndex = ListView_GetNextItem(hwndLeft, -1, LVNI_SELECTED);
        if (selectedIndex == -1)
        {
            // 没有选中文件，显示提示文本
            SetControlTextUTF8(hwndRight, g_strings.previewPrompt);
        }
        else
        {
            // 有选中文件，重新预览以更新可能的错误信息文本
            PreviewSelectedFile();
        }
    }

    // 刷新ListView以更新显示（包括文件夹大小文本）
    if (hwndLeft && IsWindow(hwndLeft))
    {
        InvalidateRect(hwndLeft, NULL, TRUE);
        // 强制重新绘制所有项目以更新文件夹大小显示
        ListView_RedrawItems(hwndLeft, 0, ListView_GetItemCount(hwndLeft) - 1);
        UpdateWindow(hwndLeft);
    }
}

// 更新列标题
void UpdateColumnHeaders()
{
    if (!hwndLeft || !IsWindow(hwndLeft))
        return;

    LVCOLUMNW lvc = {0};
    lvc.mask = LVCF_TEXT;

    for (int i = 0; i < 4; i++)
    {
        lvc.pszText = (LPWSTR)g_strings.columnNames[i];
        ListView_SetColumn(hwndLeft, i, &lvc);
    }
}

// 更新窗口标题
void UpdateWindowTitle()
{
    if (hwndMain && IsWindow(hwndMain))
    {
        SetWindowTextW(hwndMain, g_strings.windowTitle);
    }
}

// 设置状态栏文本
void SetStatusBarText(const WCHAR *message)
{
    if (hwndStatus && IsWindow(hwndStatus))
    {
        SendMessageW(hwndStatus, SB_SETTEXTW, 0, (LPARAM)message);
    }
}
