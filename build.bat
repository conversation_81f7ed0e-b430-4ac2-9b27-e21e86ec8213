@echo off
echo Building EveryView with UI optimizations...

echo Step 1: Checking for icon files...
if exist "resource\icons8_512.png" (
    echo   Found PNG icon: resource\icons8_512.png
) else (
    echo   Warning: PNG icon not found
)

echo Step 2: Compiling resource file...
windres resource.rc -o resource.o

if %errorlevel% neq 0 (
    echo Resource compilation failed! Building without icon...
    goto :compile_without_icon
)

echo Step 3: Compiling with UI optimizations...
gcc -O2 -mwindows -o EveryView.exe main.c usn_journal.c resource.o -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lshlwapi -DUNICODE -D_UNICODE

if %errorlevel% == 0 (
    echo Build successful! Generated EveryView.exe with optimizations
    del resource.o 2>nul
    goto :end
) else (
    echo Build with icon failed! Trying without icon...
    goto :compile_without_icon
)

:compile_without_icon
echo Building without icon...
gcc -O2 -mwindows -o EveryView.exe main.c usn_journal.c -lcomctl32 -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32 -lshlwapi -DUNICODE -D_UNICODE
if %errorlevel% == 0 (
    echo Build successful! Generated EveryView.exe without icon
) else (
    echo Build failed completely!
)

:end
echo Cleaning up...
if exist resource.o del resource.o

echo.
echo UI Optimizations Applied:
echo - Filter input box: Left-aligned text, vertically centered
echo - High-DPI icon support with PNG priority
echo - Improved font rendering with Unicode support
echo - Desktop icon clarity optimization
echo.
echo Features:
echo - Input text is left-aligned with 4px margin
echo - Text is vertically centered in input box
echo - Automatic DPI scaling for text positioning
echo - Maintains alignment during window resize
echo.
echo Done!
pause