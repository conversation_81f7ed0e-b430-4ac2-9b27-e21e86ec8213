#include "usn_journal.h"

static BOOL g_processing = FALSE;

// 函数声明
DWORD WINAPI DriveThreadProc(LPVOID lpParam);
int ScanDriveUSN(const WCHAR *drivePath);
int EnumerateUSNRecords(HANDLE hVolume, const WCHAR *drivePath);
int ProcessUSNBatch(BYTE *buffer, DWORD bytesReturned, const WCHAR *drivePath, MFT_ENUM_DATA *mftEnumData);
void ProcessUSNRecordFast(PUSN_RECORD_V2 usnRecord, const WCHAR *drivePath);
int FallbackDirectoryScan(const WCHAR *drivePath);
int ScanDirectoryWithDepth(const WCHAR *dirPath, int *totalCount, int maxFiles, int currentDepth, int maxDepth);

// 兼容性函数
int ReadUSNToMemory() { return ReadUSNToMemoryMultiThreaded(); }
int ScanAllDrives() { return ReadUSNToMemoryMultiThreaded(); }

// 简化的目录扫描（当USN Journal不可用时的回退方案）
int ScanDirectoryWithDepth(const WCHAR *dirPath, int *totalCount, int maxFiles, int currentDepth, int maxDepth)
{
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH * 2];

    if (currentDepth > maxDepth || *totalCount >= maxFiles)
        return 0;

    swprintf(searchPath, MAX_PATH * 2,
             (wcslen(dirPath) > 0 && dirPath[wcslen(dirPath) - 1] == L'\\') ? L"%s*" : L"%s\\*",
             dirPath);

    hFind = FindFirstFileW(searchPath, &findData);
    if (hFind == INVALID_HANDLE_VALUE)
        return 0;

    do
    {
        if (wcscmp(findData.cFileName, L".") == 0 || wcscmp(findData.cFileName, L"..") == 0)
            continue;

        if (*totalCount >= maxFiles)
            break;

        WCHAR fullPath[MAX_PATH * 2];
        swprintf(fullPath, MAX_PATH * 2,
                 (wcslen(dirPath) > 0 && dirPath[wcslen(dirPath) - 1] == L'\\') ? L"%s%s" : L"%s\\%s",
                 dirPath, findData.cFileName);

        LONGLONG fileSize = ((LONGLONG)findData.nFileSizeHigh << 32) + findData.nFileSizeLow;
        AddUSNFile(findData.cFileName, fullPath, fileSize, findData.ftLastWriteTime, findData.dwFileAttributes);
        (*totalCount)++;

        if ((findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY) &&
            wcslen(fullPath) < (MAX_PATH - 50) && currentDepth < maxDepth)
        {
            ScanDirectoryWithDepth(fullPath, totalCount, maxFiles, currentDepth + 1, maxDepth);
        }

    } while (FindNextFileW(hFind, &findData) && *totalCount < maxFiles);

    FindClose(hFind);
    return 0;
}

int ReadUSNToMemoryMultiThreaded()
{

    DWORD drives = GetLogicalDrives();
    DriveThreadData driveData[MAX_DRIVES];
    HANDLE threads[MAX_DRIVES];
    int driveCount = 0;
    int totalCount = 0;
    DWORD startTime = GetTickCount();

    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR drivePath[4] = {driveLetter, L':', L'\\', L'\0'};

            // 检查驱动器类型
            UINT driveType = GetDriveTypeW(drivePath);
            if (driveType == DRIVE_FIXED && driveCount < MAX_DRIVES)
            {
                wcscpy(driveData[driveCount].drivePath, drivePath);
                driveData[driveCount].threadId = driveCount;
                driveData[driveCount].fileCount = 0;
                driveData[driveCount].scanTime = 0;
                driveData[driveCount].success = FALSE;
                driveCount++;
            }
        }
    }

    for (int i = 0; i < driveCount; i++)
    {
        threads[i] = CreateThread(NULL, 0, DriveThreadProc, &driveData[i], 0, NULL);
        if (threads[i] == NULL)
        {
        }
        else
        {
        }
    }

    // 等待所有线程完成
    WaitForMultipleObjects(driveCount, threads, TRUE, INFINITE);

    // 收集结果
    for (int i = 0; i < driveCount; i++)
    {
        if (driveData[i].success)
            totalCount += driveData[i].fileCount;

        if (threads[i])
            CloseHandle(threads[i]);
    }

    return 0;
}

// 双线程优化版本：将驱动器分成两组并行处理
int ReadUSNToMemoryDualThreaded()
{
    DWORD drives = GetLogicalDrives();
    DriveThreadData driveData[MAX_DRIVES];
    HANDLE threads[2]; // 只使用两个线程
    int driveCount = 0;
    int totalCount = 0;
    DWORD startTime = GetTickCount();

    // 收集所有固定驱动器
    for (int i = 0; i < 26; i++)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR drivePath[4] = {driveLetter, L':', L'\\', L'\0'};

            UINT driveType = GetDriveTypeW(drivePath);
            if (driveType == DRIVE_FIXED && driveCount < MAX_DRIVES)
            {
                wcscpy(driveData[driveCount].drivePath, drivePath);
                driveData[driveCount].threadId = driveCount;
                driveData[driveCount].fileCount = 0;
                driveData[driveCount].scanTime = 0;
                driveData[driveCount].success = FALSE;
                driveCount++;
            }
        }
    }

    if (driveCount == 0)
    {
        // 没有找到固定驱动器，返回-1表示错误
        return -1;
    }

    // 创建两个线程处理组
    DualThreadData threadData[2];

    // 第一个线程处理前半部分驱动器
    threadData[0].driveData = driveData;
    threadData[0].startIndex = 0;
    threadData[0].endIndex = (driveCount + 1) / 2;
    threadData[0].threadId = 0;

    // 第二个线程处理后半部分驱动器
    threadData[1].driveData = driveData;
    threadData[1].startIndex = (driveCount + 1) / 2;
    threadData[1].endIndex = driveCount;
    threadData[1].threadId = 1;

    // 启动两个线程
    for (int i = 0; i < 2 && threadData[i].startIndex < threadData[i].endIndex; i++)
    {
        threads[i] = CreateThread(NULL, 0, DualThreadProc, &threadData[i], 0, NULL);
    }

    // 等待线程完成
    int activeThreads = (threadData[1].startIndex < threadData[1].endIndex) ? 2 : 1;
    WaitForMultipleObjects(activeThreads, threads, TRUE, INFINITE);

    // 收集结果
    for (int i = 0; i < driveCount; i++)
    {
        if (driveData[i].success)
            totalCount += driveData[i].fileCount;
    }

    // 清理线程句柄
    for (int i = 0; i < activeThreads; i++)
    {
        if (threads[i])
            CloseHandle(threads[i]);
    }

    return totalCount;
}

DWORD WINAPI DualThreadProc(LPVOID lpParam)
{
    DualThreadData *data = (DualThreadData *)lpParam;

    // 处理分配给此线程的驱动器
    for (int i = data->startIndex; i < data->endIndex; i++)
    {
        DriveThreadData *driveData = &data->driveData[i];
        DWORD startTime = GetTickCount();

        int fileCount = ScanDriveUSN(driveData->drivePath);

        driveData->scanTime = GetTickCount() - startTime;
        driveData->fileCount = fileCount;
        driveData->success = (fileCount > 0);

        if (!driveData->success)
        {
            // 回退到递归文件扫描
            int fallbackCount = FallbackDirectoryScan(driveData->drivePath);
            if (fallbackCount > 0)
            {
                driveData->fileCount = fallbackCount;
                driveData->success = TRUE;
                driveData->scanTime = GetTickCount() - startTime;
            }
        }
    }

    return 0;
}

// 高性能优化版本：使用更大缓冲区和批量处理
int ReadUSNToMemoryOptimized()
{
    DWORD drives = GetLogicalDrives();
    int totalCount = 0;
    DWORD startTime = GetTickCount();

    // 优先扫描C盘（通常是主要驱动器）
    for (int i = 2; i < 26; i++) // 从C盘开始 (C=2)
    {
        if (drives & (1 << i))
        {
            WCHAR driveLetter = L'A' + i;
            WCHAR drivePath[4] = {driveLetter, L':', L'\\', L'\0'};

            UINT driveType = GetDriveTypeW(drivePath);
            if (driveType == DRIVE_FIXED)
            {
                int fileCount = ScanDriveUSNOptimized(drivePath);
                if (fileCount > 0)
                {
                    totalCount += fileCount;
                    // 如果C盘扫描成功且找到足够文件，可以跳过其他驱动器
                    if (i == 2 && fileCount > 10000) // C盘且文件数量足够
                    {
                        break;
                    }
                }
            }
        }
    }

    // 如果没有找到足够文件，扫描其他驱动器
    if (totalCount < 1000)
    {
        for (int i = 0; i < 26; i++)
        {
            if (i == 2)
                continue; // 跳过已扫描的C盘

            if (drives & (1 << i))
            {
                WCHAR driveLetter = L'A' + i;
                WCHAR drivePath[4] = {driveLetter, L':', L'\\', L'\0'};

                UINT driveType = GetDriveTypeW(drivePath);
                if (driveType == DRIVE_FIXED)
                {
                    int fileCount = ScanDriveUSNOptimized(drivePath);
                    if (fileCount > 0)
                    {
                        totalCount += fileCount;
                    }
                }
            }
        }
    }

    return totalCount;
}

// 优化的单驱动器扫描
int ScanDriveUSNOptimized(const WCHAR *drivePath)
{
    HANDLE hVolume;
    WCHAR volumePath[MAX_PATH];

    swprintf(volumePath, MAX_PATH, L"\\\\.\\%c:", drivePath[0]);

    hVolume = CreateFileW(volumePath,
                          GENERIC_READ,
                          FILE_SHARE_READ | FILE_SHARE_WRITE,
                          NULL,
                          OPEN_EXISTING,
                          FILE_FLAG_NO_BUFFERING, // 禁用系统缓冲以提高性能
                          NULL);

    if (hVolume == INVALID_HANDLE_VALUE)
        return 0;

    // 查询USN Journal信息
    USN_JOURNAL_DATA journalData;
    DWORD bytesReturned;

    if (!DeviceIoControl(hVolume,
                         FSCTL_QUERY_USN_JOURNAL,
                         NULL, 0,
                         &journalData, sizeof(journalData),
                         &bytesReturned,
                         NULL))
    {
        CloseHandle(hVolume);
        return FallbackDirectoryScan(drivePath); // 立即回退
    }

    // 使用更大的缓冲区提高性能
    int fileCount = EnumerateUSNRecordsOptimized(hVolume, drivePath);

    CloseHandle(hVolume);
    return fileCount;
}

DWORD WINAPI DriveThreadProc(LPVOID lpParam)
{
    DriveThreadData *data = (DriveThreadData *)lpParam;
    DWORD startTime = GetTickCount();

    int fileCount = ScanDriveUSN(data->drivePath);

    data->scanTime = GetTickCount() - startTime;
    data->fileCount = fileCount;
    data->success = (fileCount > 0);

    if (!data->success)
    {
        // 回退到递归文件扫描
        int fallbackCount = FallbackDirectoryScan(data->drivePath);
        if (fallbackCount > 0)
        {
            data->fileCount = fallbackCount;
            data->success = TRUE;
            data->scanTime = GetTickCount() - startTime;
        }
    }

    return 0;
}

int ScanDriveUSN(const WCHAR *drivePath)
{
    HANDLE hVolume;
    WCHAR volumePath[MAX_PATH];

    // 构建卷路径 \\.\C:
    swprintf(volumePath, MAX_PATH, L"\\\\.\\%c:", drivePath[0]);

    // 打开卷
    hVolume = CreateFileW(volumePath,
                          GENERIC_READ,
                          FILE_SHARE_READ | FILE_SHARE_WRITE,
                          NULL,
                          OPEN_EXISTING,
                          0,
                          NULL);

    if (hVolume == INVALID_HANDLE_VALUE)
        return 0;

    // 查询USN Journal信息
    USN_JOURNAL_DATA journalData;
    DWORD bytesReturned;

    if (!DeviceIoControl(hVolume,
                         FSCTL_QUERY_USN_JOURNAL,
                         NULL, 0,
                         &journalData, sizeof(journalData),
                         &bytesReturned,
                         NULL))
    {
        CloseHandle(hVolume);
        return 0;
    }

    // 枚举USN记录
    int fileCount = EnumerateUSNRecords(hVolume, drivePath);

    CloseHandle(hVolume);
    return fileCount;
}

int EnumerateUSNRecords(HANDLE hVolume, const WCHAR *drivePath)
{
    // 使用动态分配的大缓冲区
    BYTE *buffer = (BYTE *)malloc(USN_BUFFER_SIZE);
    if (!buffer)
        return 0;

    MFT_ENUM_DATA mftEnumData = {0};
    DWORD bytesReturned;
    int fileCount = 0;
    int batchCount = 0;
    DWORD startTime = GetTickCount();

    // 设置枚举参数
    mftEnumData.StartFileReferenceNumber = 0;
    mftEnumData.LowUsn = 0;
    mftEnumData.HighUsn = MAXLONGLONG;

    while (TRUE)
    {
        // 枚举USN记录
        if (!DeviceIoControl(hVolume,
                             FSCTL_ENUM_USN_DATA,
                             &mftEnumData, sizeof(mftEnumData),
                             buffer, USN_BUFFER_SIZE,
                             &bytesReturned,
                             NULL))
        {
            DWORD error = GetLastError();
            if (error == ERROR_HANDLE_EOF)
                break;
            else
                break;
        }

        if (bytesReturned < sizeof(USN))
            break;

        // 批量处理USN记录（优化：减少函数调用开销）
        int batchProcessed = ProcessUSNBatch(buffer, bytesReturned, drivePath, &mftEnumData);
        fileCount += batchProcessed;
        batchCount++;

        // 每处理1000批次检查一次，避免过度占用CPU
        if (batchCount % 1000 == 0)
        {
            Sleep(1); // 让出CPU时间片
        }

        // 限制文件数量避免内存溢出
        if (fileCount >= 4069000)
            break;
    }

    free(buffer);
    return fileCount;
}

// 优化的USN记录枚举 - 使用更大缓冲区和批量处理
int EnumerateUSNRecordsOptimized(HANDLE hVolume, const WCHAR *drivePath)
{
    // 使用2MB缓冲区（比原来大4倍）
    const DWORD LARGE_BUFFER_SIZE = 2 * 1024 * 1024;
    BYTE *buffer = (BYTE *)malloc(LARGE_BUFFER_SIZE);
    if (!buffer)
        return 0;

    MFT_ENUM_DATA mftEnumData = {0};
    DWORD bytesReturned;
    int fileCount = 0;
    int batchCount = 0;
    DWORD startTime = GetTickCount();

    // 设置枚举参数
    mftEnumData.StartFileReferenceNumber = 0;
    mftEnumData.LowUsn = 0;
    mftEnumData.HighUsn = MAXLONGLONG;

    while (TRUE)
    {
        // 枚举USN记录
        if (!DeviceIoControl(hVolume,
                             FSCTL_ENUM_USN_DATA,
                             &mftEnumData, sizeof(mftEnumData),
                             buffer, LARGE_BUFFER_SIZE,
                             &bytesReturned,
                             NULL))
        {
            DWORD error = GetLastError();
            if (error == ERROR_HANDLE_EOF)
                break;
            else
                break;
        }

        if (bytesReturned < sizeof(USN))
            break;

        // 批量处理USN记录（优化：减少函数调用开销）
        int batchProcessed = ProcessUSNBatchOptimized(buffer, bytesReturned, drivePath, &mftEnumData);
        fileCount += batchProcessed;
        batchCount++;

        // 限制文件数量避免内存溢出
        if (fileCount >= 4069000)
            break;

        // 每处理500批次检查一次，减少CPU占用
        if (batchCount % 500 == 0)
        {
            Sleep(0); // 让出时间片
        }
    }

    free(buffer);
    return fileCount;
}

int ProcessUSNBatch(BYTE *buffer, DWORD bytesReturned, const WCHAR *drivePath, MFT_ENUM_DATA *mftEnumData)
{
    int processedCount = 0;
    PUSN_RECORD_V2 usnRecord;
    DWORD offset = sizeof(USN);

    while (offset < bytesReturned)
    {
        usnRecord = (PUSN_RECORD_V2)(buffer + offset);

        if (usnRecord->RecordLength == 0 || usnRecord->RecordLength > (bytesReturned - offset))
            break;

        // 快速处理USN记录
        ProcessUSNRecordFast(usnRecord, drivePath);
        processedCount++;

        // 更新下一个记录的起始位置
        mftEnumData->StartFileReferenceNumber = usnRecord->FileReferenceNumber;

        // 移动到下一个记录
        offset += usnRecord->RecordLength;
    }

    return processedCount;
}

// 优化的批处理函数 - 减少函数调用开销
int ProcessUSNBatchOptimized(BYTE *buffer, DWORD bytesReturned, const WCHAR *drivePath, MFT_ENUM_DATA *mftEnumData)
{
    int processedCount = 0;
    PUSN_RECORD_V2 usnRecord;
    DWORD offset = sizeof(USN);

    while (offset < bytesReturned)
    {
        usnRecord = (PUSN_RECORD_V2)(buffer + offset);

        if (usnRecord->RecordLength == 0 || usnRecord->RecordLength > (bytesReturned - offset))
            break;

        // 内联处理USN记录，避免函数调用开销
        if (!(usnRecord->FileAttributes & FILE_ATTRIBUTE_DIRECTORY) &&
            !(usnRecord->FileAttributes & (FILE_ATTRIBUTE_SYSTEM | FILE_ATTRIBUTE_HIDDEN)))
        {
            WCHAR *fileName = (WCHAR *)((BYTE *)usnRecord + usnRecord->FileNameOffset);
            int fileNameLength = usnRecord->FileNameLength / sizeof(WCHAR);

            // 快速过滤临时文件
            if (fileNameLength > 4)
            {
                WCHAR *ext = fileName + fileNameLength - 4;
                if (!(wcsncmp(ext, L".tmp", 4) == 0 || wcsncmp(ext, L".log", 4) == 0))
                {
                    // 构建完整路径
                    WCHAR fullPath[MAX_PATH];
                    swprintf(fullPath, MAX_PATH, L"%ls%.*ls", drivePath, fileNameLength, fileName);

                    // 转换时间戳
                    FILETIME lastWriteTime;
                    lastWriteTime.dwLowDateTime = usnRecord->TimeStamp.LowPart;
                    lastWriteTime.dwHighDateTime = usnRecord->TimeStamp.HighPart;

                    // 添加到内存数组
                    AddUSNFile(fileName, fullPath, 0, lastWriteTime, usnRecord->FileAttributes);
                }
            }
        }

        processedCount++;

        // 更新下一个记录的起始位置
        mftEnumData->StartFileReferenceNumber = usnRecord->FileReferenceNumber;

        // 移动到下一个记录
        offset += usnRecord->RecordLength;
    }

    return processedCount;
}

void ProcessUSNRecordFast(PUSN_RECORD_V2 usnRecord, const WCHAR *drivePath)
{
    // 跳过目录和系统文件，只处理普通文件
    if (usnRecord->FileAttributes & FILE_ATTRIBUTE_DIRECTORY)
        return;

    if (usnRecord->FileAttributes & (FILE_ATTRIBUTE_SYSTEM | FILE_ATTRIBUTE_HIDDEN))
        return;

    // 快速提取文件名（避免不必要的复制）
    WCHAR *fileName = (WCHAR *)((BYTE *)usnRecord + usnRecord->FileNameOffset);
    int fileNameLength = usnRecord->FileNameLength / sizeof(WCHAR);

    // 跳过临时文件和系统文件
    if (fileNameLength > 4)
    {
        WCHAR *ext = fileName + fileNameLength - 4;
        if (wcsncmp(ext, L".tmp", 4) == 0 || wcsncmp(ext, L".log", 4) == 0)
            return;
    }

    // 构建完整路径（优化：减少字符串操作）
    WCHAR fullPath[MAX_PATH];
    swprintf(fullPath, MAX_PATH, L"%ls%.*ls", drivePath, fileNameLength, fileName);

    // 获取文件大小（USN记录中没有直接的文件大小信息，设为0）
    LONGLONG fileSize = 0;

    // 转换时间戳
    FILETIME lastWriteTime;
    lastWriteTime.dwLowDateTime = usnRecord->TimeStamp.LowPart;
    lastWriteTime.dwHighDateTime = usnRecord->TimeStamp.HighPart;

    // 添加到内存数组
    AddUSNFile(fileName, fullPath, fileSize, lastWriteTime, usnRecord->FileAttributes);
}

int FallbackDirectoryScan(const WCHAR *drivePath)
{
    int totalCount = 0;
    int maxFiles = 4069000;

    // 使用带深度控制的递归扫描函数
    ScanDirectoryWithDepth(drivePath, &totalCount, maxFiles, 0, 16);

    return totalCount;
}

int SimpleDirectoryScan(const WCHAR *drivePath)
{
    int totalCount = 0;
    WIN32_FIND_DATAW findData;
    HANDLE hFind;
    WCHAR searchPath[MAX_PATH];

    swprintf(searchPath, MAX_PATH, L"%s*", drivePath);
    hFind = FindFirstFileW(searchPath, &findData);

    if (hFind != INVALID_HANDLE_VALUE)
    {
        do
        {
            if (wcscmp(findData.cFileName, L".") != 0 && wcscmp(findData.cFileName, L"..") != 0)
            {
                WCHAR fullPath[MAX_PATH * 2];
                swprintf(fullPath, MAX_PATH * 2, L"%s%s", drivePath, findData.cFileName);

                LONGLONG fileSize = ((LONGLONG)findData.nFileSizeHigh << 32) + findData.nFileSizeLow;
                AddUSNFile(findData.cFileName, fullPath, fileSize, findData.ftLastWriteTime, findData.dwFileAttributes);
                totalCount++;

                if (totalCount >= 4069000)
                    break;
            }
        } while (FindNextFileW(hFind, &findData));

        FindClose(hFind);
    }

    return totalCount;
}
